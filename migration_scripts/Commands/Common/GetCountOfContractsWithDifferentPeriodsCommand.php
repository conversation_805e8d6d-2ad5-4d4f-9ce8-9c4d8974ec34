<?php

namespace TF\Commands\Common;

use PDO;

class GetCountOfContractsWithDifferentPeriodsCommand extends UserDbCommand
{
    private static $tmpTableName = 'tmp_contract_duration_summary';

    public function onDbExecute(PDO $pdo, $output, $input)
    {
        $contractsDuractions = $this->getContractsDuractions($pdo);

        if (!empty($contractsDuractions)) {
            $sql = $this->mainConnection->prepare('
                    INSERT INTO ' . self::$tmpTableName . ' (database, "1_year", "2_years", "3_years", "4_years", "5_years", "6_years", "7_years", "8_years", "9_years", "10_years", "more_than_10_years", "invalid_or_missing")
                    VALUES (:database, :1_year, :2_years, :3_years, :4_years, :5_years, :6_years, :7_years, :8_years, :9_years, :10_years, :more_than_10_years, :invalid_or_missing);
                ');

            foreach ($contractsDuractions as $contractsDuraction) {
                $sql->execute([
                    'database' => $this->userDbName,
                    '1_year' => $contractsDuraction['1_year'],
                    '2_years' => $contractsDuraction['2_years'],
                    '3_years' => $contractsDuraction['3_years'],
                    '4_years' => $contractsDuraction['4_years'],
                    '5_years' => $contractsDuraction['5_years'],
                    '6_years' => $contractsDuraction['6_years'],
                    '7_years' => $contractsDuraction['7_years'],
                    '8_years' => $contractsDuraction['8_years'],
                    '9_years' => $contractsDuraction['9_years'],
                    '10_years' => $contractsDuraction['10_years'],
                    'more_than_10_years' => $contractsDuraction['more_than_10_years'],
                    'invalid_or_missing' => $contractsDuraction['invalid_or_missing'],
                ]);
            }

            $output->writeln('Successfully added database ' . $this->userDbName . ' in the statistic.' . PHP_EOL);
        } else {
            $output->writeln('In database ' . $this->userDbName . ' has no contracts' . PHP_EOL);
        }
    }

    protected function onCommandStart($input, $output)
    {
        $sql = $this->mainConnection->prepare('
            CREATE TABLE IF NOT EXISTS public.' . self::$tmpTableName . ' (
                database varchar NULL,
                "1_year" integer DEFAULT 0,
                "2_years" integer DEFAULT 0,
                "3_years" integer DEFAULT 0,
                "4_years" integer DEFAULT 0,
                "5_years" integer DEFAULT 0,
                "6_years" integer DEFAULT 0,
                "7_years" integer DEFAULT 0,
                "8_years" integer DEFAULT 0,
                "9_years" integer DEFAULT 0,
                "10_years" integer DEFAULT 0,
                "more_than_10_years" integer DEFAULT 0,
                "invalid_or_missing" integer DEFAULT 0
            );
        ');
        $sql->execute();

        $sql = $this->mainConnection->prepare('
            TRUNCATE ' . self::$tmpTableName . ';
        ');
        $sql->execute();
    }

    protected function configure()
    {
        parent::configure();
        $this
            ->setName('tf:generate_statistic_of_contracts_duration')
            ->setDescription('Get count of different contracts durations and log the result in temp table ' . self::$tmpTableName . ' in susi_main');
    }

    /**
     * @param PDO $userDb
     * @param string $tableName
     *
     * @return array|bool
     */
    protected function getContractsDuractions($pdo)
    {
        $cmd = $pdo->prepare('
            SELECT
                COUNT(*) FILTER (WHERE duration_years = 1) AS "1_year",
                COUNT(*) FILTER (WHERE duration_years = 2) AS "2_years",
                COUNT(*) FILTER (WHERE duration_years = 3) AS "3_years",
                COUNT(*) FILTER (WHERE duration_years = 4) AS "4_years",
                COUNT(*) FILTER (WHERE duration_years = 5) AS "5_years",
                COUNT(*) FILTER (WHERE duration_years = 6) AS "6_years",
                COUNT(*) FILTER (WHERE duration_years = 7) AS "7_years",
                COUNT(*) FILTER (WHERE duration_years = 8) AS "8_years",
                COUNT(*) FILTER (WHERE duration_years = 9) AS "9_years",
                COUNT(*) FILTER (WHERE duration_years = 10) AS "10_years",
                COUNT(*) FILTER (WHERE duration_years > 10) AS "more_than_10_years",
                COUNT(*) FILTER (WHERE duration_years IS NULL) AS "invalid_or_missing"
            FROM (
                SELECT
                    CEIL(EXTRACT(EPOCH FROM (due_date - start_date)) / (365.25 * 24 * 60 * 60))::int AS duration_years
                FROM
                    su_contracts
                WHERE
                    start_date IS NOT NULL
                    AND due_date IS NOT null
            ) AS durations;
        ');
        $cmd->execute();

        return $cmd->fetchAll(PDO::FETCH_ASSOC);
    }
}
