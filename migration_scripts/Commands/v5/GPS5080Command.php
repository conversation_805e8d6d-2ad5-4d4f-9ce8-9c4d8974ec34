<?php

namespace TF\Commands\v5;

use PDO;
use TF\Commands\Common\MainDbCommand;

/**
 * GPS-5080 command run on all databases.
 * Add 'ubbxml' value to masspayment_type_enum.
 */
class GPS5080Command extends MainDbCommand
{
    public function onDbExecute(PDO $pdo, $output, $input)
    {
        $pdo->exec("ALTER TYPE masspayment_type_enum ADD VALUE IF NOT EXISTS 'ubbxml';");
    }

    protected function configure()
    {
        parent::configure();
        $this
            ->setName('v5:GPS-5080')
            ->setDescription('Add \'ubbxml\' value to masspayment_type_enum');
    }
}
