<?php

namespace TF\Commands\v5;

use PDO;
use TF\Commands\Common\UserDbCommand;

class GPS5161Command extends UserDbCommand
{
    protected function configure()
    {
        parent::configure();
        $this
            ->setName('v5:GPS-5161')
            ->setDescription('Create indexes for columns plot_id and contract_id in su_contracts_plots_rel table');
    }

    protected function onDbExecute(PDO $pdo, $output, $input)
    {
        $pdo->exec('CREATE INDEX idx_contracts_plots_plot_id ON su_contracts_plots_rel(plot_id);');
        $pdo->exec('CREATE INDEX idx_contracts_plots_contract_id ON su_contracts_plots_rel(contract_id);');
    }
}
