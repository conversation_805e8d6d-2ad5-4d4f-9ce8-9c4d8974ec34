function initSubleasesPlotsGrid(sublease_id) {

    var subleasesPlotsGrid = jQuery('#sublease-plots-tables'),
        isDatagridBound = subleasesPlotsGrid.data().hasOwnProperty('datagrid');

    if (isDatagridBound) {
        if (sublease_id !== 0) {
            subleasesPlotsGrid.datagrid({
                url: 'index.php?subleases-rpc=sublease-plots-grid',
                rpcParams: [{
                    type: 'view',
                    sublease_id: sublease_id,
                }],
            });
        } else {
            subleasesPlotsGrid.datagrid('loadData',
                {
                    rows: [],
                    total: 0,
                    footer: [
                        {
                            area_type : '<b>ОБЩО</b>',
                            used_area : '',
                            area : ''
                        }
                    ]
                }
            );
        }

        return;
    }

    subleasesPlotsGrid.datagrid({
        iconCls: 'icon-edit-geometry',
        nowrap: true,
        title: 'Имоти',
        autoRowHeight: true,
        striped: true,
        pageSize: 10,
        fit: true,
        singleSelect: true,
        fitColumns: true,
        showFooter: true,
        rowStyler: function (index, row) {
            var style = [];
            if (row.is_plot_active == false) {
                style.push('color: #aaa');
            }

            if (row.has_problem == true) {
                style.push('background-color: #ffcccc');
            }

            return style.join(';');
        },
        loader : EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
        loadFilter : EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter,
        columns: [
            [
                {
                    field: 'land',
                    title: '<b>Землище</b>',
                    sortable: true,
                    width: 150,
                    rowspan: 2
                }
                , {
                    field: 'kad_ident',
                    title: '<b>Идентификатор</b>',
                    sortable: true,
                    width: 150,
                    rowspan: 2
                }
                , {
                    field: 'category',
                    title: '<b>Категория</b>',
                    sortable: true,
                    width: 100,
                    rowspan: 2
                }
                , {
                    field: 'area_type',
                    title: '<b>НТП</b>',
                    sortable: true,
                    width: 150,
                    rowspan: 2
                } 
                , {
                    field: 'comment',
                    title: '<b>Забележка</b>',
                    sortable: false,
                    align: 'center',
                    width: 150,
                    rowspan: 2,
                    editor: {
                        type: 'text',
                        options: {}
                    }
                }
                , {
                    title: '<b>Площ (дка)</b>',
                    colspan: 4
                }
                , {
                    field: 'sales_contracts',
                    title: '<b>Договори за продажба</b>',
                    sortable: false,
                    align: 'center',
                    width: 250,
                    rowspan: 2
                }
            ],
            [
                {
                    field: 'pc_area',
                    title: '<b>по договор</b>',
                    sortable: true,
                    width: 90
                }
                ,{
                    field: 'rent_area',
                    title: '<b>по рента</b>',
                    sortable: true,
                    width: 90
                }
                , {
                    field: 'document_area',
                    title: '<b>по документ</b>',
                    sortable: true,
                    width: 90
                }
                , {
                    field: 'allowable_area',
                    title: '<b>по сечение</b>',
                    sortable: true,
                    width: 90
                }
            ]
        ],
        pagination: false,
        rownumbers: true,
        toolbar: '#sublese-plots-toolbar',
        onBeforeLoad: function () {
            subleasesPlotsGrid.datagrid('clearChecked');
        },
        onLoadSuccess: function () {
            var plotData = jQuery('#sublease-plots-tables').datagrid('getData'),
                selectedSublease = jQuery('#subleases-tree').tree('getSelected');

            if (selectedSublease) {
                var sublease_id = selectedSublease.id;

                if (plotData.rows[0]) {
                    jQuery('#sublease-plots-tables').datagrid('selectRow', 0);
                } else {
                    initSubleasesContractsGrid(sublease_id, 0);
                }
            }
        },
        onSelect: function (rowIndex, rowData) {
            var selectedSublease = jQuery('#subleases-tree').tree('getSelected');
            if (selectedSublease) {
                sublease_id = selectedSublease.id;
                initSubleasesContractsGrid(sublease_id, rowData.gid);
            };
        }
    });
}

function initSubleasesPlotsAddGrid(sublease_id) {
    var subleaseData = jQuery('#subleases-tree').tree('getSelected');

    jQuery('#sublease-plots-add-tables').datagrid({
        iconCls: 'icon-plots',
        nowrap: true,
        autoRowHeight: true,
        striped: true,
        pageSize: 10,
        fit: true,
        border: false,
        showFooter: true,
        url: 'index.php?subleases-rpc=sublease-plots-grid',
        rpcMethod: 'addPlots',
        rpcParams: [{
            type: 'add',
            sublease_id: sublease_id,
            sublease_farming_id: subleaseData.attributes.farming_id,
            sublease_due_date: subleaseData.attributes.due_date,
            sublease_start_date: subleaseData.attributes.start_date,
            exclude_subleased_area: true
        }],
        loader : EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
        loadFilter : EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter,
        sortName: 'gid',
        sortOrder: 'asc',
        idField: 'pc_rel_id',
        rowStyler: function (index, row) {
            var style = [];
            if (row.is_edited == true) {
                style.push('color: #aaa');
            }

            return style.join(';');
        },
        frozenColumns: [[
            {
                field: 'ck',
                checkbox: true
            }
        ]],
        columns: [[
            {
                field: 'land',
                title: '<b>Землище</b>',
                sortable: true,
                width: 150,
                rowspan: 2
            }, {
                field: 'kad_ident',
                title: '<b>Идентификатор</b>',
                width: 150,
                rowspan: 2
            }, {
                field: 'area_type',
                title: '<b>НТП</b>',
                sortable: true,
                width: 150,
                rowspan: 2
            }, {
                field: 'c_num',
                title: '<b>Договор</b>',
                sortable: true,
                align: 'center',
                width: 150,
                rowspan: 2
            }, {
                field: 'comment',
                title: '<b>Забележка</b>',
                sortable: false,
                align: 'center',
                width: 150,
                rowspan: 2,
                editor: {
                    type: 'text',
                    options: {}
                }
            }, {
                title: '<b>Площ (дка)</b>',
                width: 150,
                colspan: 5
            }],
            [
                {
                    field: 'pc_area',
                    title: '<b>Договор</b>',
                    sortable: true,
                    width: 90,
                    editor: {
                        type: 'numberbox',
                        options: {
                            required: true,
                            missingMessage: 'Полето е задължително',
                            precision: 3
                        }
                    }
                },
                {
                    field: 'document_area',
                    title: '<b>Документ</b>',
                    sortable: true,
                    width: 90
                },
                {
                    field: 'rent_area',
                    title: '<b>Рента</b>',
                    sortable: true,
                    width: 90,
                    editor: {
                        type: 'numberbox',
                        options: {
                            missingMessage: 'Полето е задължително',
                            precision: 3
                        }
                    }
                },
                {
                    field: 'used_area',
                    title: '<b>Изп.</b>',
                    sortable: true,
                    width: 90
                }, {
                    field: 'allowable_area',
                    title: '<b>Сечение</b>',
                    sortable: true,
                    width: 90
                }]
            ],
        pagination: true,
        rownumbers: true,
        toolbar: [{
            id: 'btnaddcontractplotrelation',
            text: 'Добавяне',
            iconCls: 'icon-add',
            handler: function () {
                if (!hasPlotRightsRW) {
                    messagerPlotsWriteRights();
                    return false;
                }
                var getChecked = jQuery('#sublease-plots-add-tables').datagrid('getChecked');
                if (getChecked[0]) {
                    var obj = {},
                        pc_rel_id_array = [],
                        pc_rel_data_array = [],
                        comments = {},
                        plots_doc_area = {},
                        plots_contract_area = {},
                        plots_rent_area = {},
                        plots_contract_area_sum = {},
                        index,
                        is_selected_historical_plot = false,
                        subleasesPlotsAddTables = jQuery('#sublease-plots-add-tables');
                    for (var i = 0; i < getChecked.length; i++)
                    {
                        index = subleasesPlotsAddTables.datagrid('getRowIndex', getChecked[i]);
                        let row_pc_area = true;
                        let row_rent_area = true;

                        let pc_area = subleasesPlotsAddTables.datagrid('getEditor', {
                            index: index,
                            field: 'contract_area'
                        });
                        if(pc_area){
                            row_pc_area = jQuery(pc_area.target).combobox('getText');
                        }

                        let rent_area = subleasesPlotsAddTables.datagrid('getEditor', {
                            index: index,
                            field: 'rent_area'
                        });

                        if(rent_area) {
                            row_rent_area = jQuery(rent_area.target).combobox('getText');
                        }

                        if (!row_pc_area || !row_rent_area) {
                            jQuery.messager.alert('Грешка', 'Площ по договор и Площ за рента са задължителни полета.', 'error');
                            return;
                        }

                        jQuery('#sublease-plots-add-tables').datagrid('endEdit', index);

                        const pcArea = parseFloat(getChecked[i].pc_area).toFixed(3);
                        const rentArea = parseFloat(getChecked[i].rent_area).toFixed(3);
                        const docArea = parseFloat(getChecked[i].document_area).toFixed(3);
                        if (parseFloat(pcArea) > parseFloat(docArea)) {
                            const msg = TF.Rpc.ExceptionsList.WRONG_PLOT_AREA.message + `Идентификатор: ${getChecked[i].kad_ident}`
                            jQuery.messager.alert('Грешка', msg, 'error');
                            return;
                        }

                        if(parseFloat(rentArea) > parseFloat(pcArea)){
                            const msg = TF.Rpc.ExceptionsList.WRONG_RENT_AREA.message + `Идентификатор: ${getChecked[i].kad_ident}`
                            jQuery.messager.alert('Грешка', msg, 'error');
                            return;
                        }

                        pc_rel_id_array.push(getChecked[i].pc_rel_id);

                        pc_rel_data_array.push(getChecked[i].kad_ident + ' (' + getChecked[i].c_num + ')');
                        if(getChecked[i].document_area != null){
                            plots_doc_area[getChecked[i].gid] = getChecked[i].document_area;
                        }
                        if(getChecked[i].rent_area != null){
                            if (!plots_rent_area.hasOwnProperty(getChecked[i].gid)) {
                                plots_rent_area[getChecked[i].gid] = 0;
                            }
                            plots_rent_area[getChecked[i].gid] += parseFloat(getChecked[i].rent_area);
                        }

                        if (getChecked[i].pc_area != null) {
                            if (!plots_contract_area_sum.hasOwnProperty(getChecked[i].gid)) {
                                plots_contract_area_sum[getChecked[i].gid] = 0;
                            }
                              plots_contract_area_sum[getChecked[i].gid] += parseFloat(getChecked[i].pc_area);
                        }

                        if(getChecked[i].pc_area != null){
                            if (!plots_contract_area[getChecked[i].gid]) {
                                plots_contract_area[getChecked[i].gid] = parseFloat(getChecked[i].pc_area);
                            } else {
                                plots_contract_area[getChecked[i].gid] += parseFloat(getChecked[i].pc_area);
                            }

                            if (plots_contract_area[getChecked[i].gid] > plots_doc_area[getChecked[i].gid]) {
                                plots_contract_area[getChecked[i].gid] = plots_doc_area[getChecked[i].gid];
                            }
                        }
                        if(getChecked[i].is_edited == true && is_selected_historical_plot == false) {
                            is_selected_historical_plot = true;
                        }
                        if(getChecked[i].comment != null) {
                            comments[getChecked[i].gid] = getChecked[i].comment;
                        }
                    }

                    obj.pc_rel_id_array = pc_rel_id_array;
                    obj.pc_rel_data_array = pc_rel_data_array;
                    obj.sublease_id = sublease_id;
                    obj.plots_doc_area = plots_doc_area;
                    obj.plots_rent_area = plots_rent_area;
                    obj.plots_contract_area = plots_contract_area;
                    obj.plots_contract_area_sum = plots_contract_area_sum;
                    obj.comments = comments;

                    if (is_selected_historical_plot == true) {
                        jQuery.messager.confirm('Потвърждение', 'Избраните имоти включват исторически имоти,'+
                        ' които не са част от актуалната КВС/КК. Желаете ли да продължите?', function(r) {
                            if (r) {
                                //form values
                                TF.Rpc.Subleases
                                .SublesePlotsGrid
                                .addSPCRelations(obj)
                                .done(function (dataObj) {
                                  
                                })
                                .fail(function (errorObj) {
                                  return showAddContractPlotAreaErrorMessage(errorObj);
                                });

                                setTimeout(function(){
                                    jQuery('#sublease-plots-add-tables').datagrid('uncheckAll');
                                    jQuery('#sublease-plots-add-tables').datagrid('unselectAll');
                                    jQuery('#sublease-plots-add-tables').datagrid('loadRpc');
                                    jQuery('#sublease-plots-tables').datagrid('loadRpc');
                                },1000);
                            }
                        });
                    }else
                    {
                       //form values
                        TF.Rpc.Subleases
                        .SublesePlotsGrid
                        .addSPCRelations(obj)
                        .done(function (dataObj) {
                  
                        })
                        .fail(function (errorObj) {
                            return showAddContractPlotAreaErrorMessage(errorObj);
                        });


                        setTimeout(function(){
                            jQuery('#sublease-plots-add-tables').datagrid('uncheckAll');
                            jQuery('#sublease-plots-add-tables').datagrid('unselectAll');
                            jQuery('#sublease-plots-add-tables').datagrid('loadRpc');
                            jQuery('#sublease-plots-tables').datagrid('loadRpc');
                        },1000);
                       
                    }
                } else
                {
                    jQuery.messager.alert('Грешка', 'Моля изберете парцели, който да бъдат добавени към договора.');
                }
            }
        }, {
            id: 'btnaddcontractplotfilter',
            text: 'Филтриране',
            iconCls: 'icon-filter',
            handler: function() {
                jQuery('#win-add-plots-filter').window('open');
            }
        }, {
            id: 'btnaddcontractplotfilterclear',
            text: 'Покажи всички',
            iconCls: 'icon-clear-filter',
            handler: function() {
                clearAddPlotGridFilter();
            }
        }],
        onBeforeLoad: function() {
//          jQuery('#sublease-plots-add-tables').datagrid('clearChecked');
        },
        onSelect: function (index) {
            jQuery('#sublease-plots-add-tables').datagrid('beginEdit', index);
        },
        onUnselect: function (index) {
            jQuery('#sublease-plots-add-tables').datagrid('cancelEdit', index);
        }
    });
}

/**
 *
 * @param errorObj
 * @returns {*}
 */
function showAddContractPlotAreaErrorMessage(errorObj)
{
    if (errorObj.is(TF.Rpc.ExceptionsList.CONTRACT_AREA_EXCEEDS_PLOT_AREA)) {
        const plotIdent = errorObj.getData();
        const errorMsg = `В избрания списък има имоти, които са с по-малка площ по документ от сумарната площ по договор: ${plotIdent}.Преди да продължите уверете се, че площта по договор е по-малка или равна на площта по документ`;

        return jQuery.messager.alert('Грешка', errorMsg, 'error');
    }

    if(errorObj.is(TF.Rpc.ExceptionsList.CONTRACT_RENT_AREA_EXCEEDS_PLOT_AREA)) {
        return jQuery.messager.alert('Грешка', TF.Rpc.ExceptionsList.CONTRACT_RENT_AREA_EXCEEDS_PLOT_AREA.message, 'error');
    }
}

function filterAddPlotGrid() {
    if (jQuery('#search-ekatte > input').combobox('getValue')
            || jQuery('#search-masiv > input').val().length != 0
            || jQuery('#search-number > input').val().length != 0
            || jQuery('#search-ident').val().length != 0
            || jQuery('#search-c-num').val().length != 0
    )
    {
        var subleaseData = jQuery('#subleases-tree').tree('getSelected');

        jQuery('#sublease-plots-add-tables').datagrid({
            rpcParams: [{
                filters: {
                    ekate: jQuery('#search-ekatte > input').combobox('getValue'),
                    masiv: jQuery('#search-masiv > input').val(),
                    number: jQuery('#search-number > input').val(),
                    kad_ident: jQuery('#search-ident').val(),
                    c_num: jQuery('#search-c-num').val(),
                },
                type: 'add',
                sublease_id: jQuery('#subleases-tree').tree('getSelected').id,
                sublease_farming_id: subleaseData['attributes'].farming_id,
                sublease_due_date: subleaseData['attributes'].due_date,
                sublease_start_date: subleaseData['attributes'].start_date,
                exclude_subleased_area: true
            }]
        });
        jQuery('#win-add-plots-filter').window('close');
        return true;
    } else {
        jQuery.messager.alert('Грешка', 'Моля въведете параметри за филтриране.');
        return false;
    }
}

function clearAddPlotGridFilter() {
    var subleaseData = jQuery('#subleases-tree').tree('getSelected');

    jQuery('#search-ekatte > input').combobox('reset');
    jQuery('#search-masiv > input').val('');
    jQuery('#search-number > input').val('');
    jQuery('#search-ident').val('');
    jQuery('#search-c-num').val('');
    jQuery('#sublease-plots-add-tables').datagrid({
        rpcParams: [{
            sublease_farming_id: subleaseData['attributes'].farming_id,
            sublease_due_date: subleaseData['attributes'].due_date,
            sublease_start_date: subleaseData['attributes'].start_date,
            type: 'add',
            sublease_id: jQuery('#subleases-tree').tree('getSelected').id,
            exclude_subleased_area: true,
        }]
    });
}

function addSubleasedPlots(result) {
    TF.Rpc.Subleases
    .SublesePlotsGrid
    .addConfirmedSPCRelation()
    .done(function (dataObj) {
        jQuery('#sublease-plots-add-tables').datagrid('loadRpc');
        jQuery('#sublease-plots-tables').datagrid('loadRpc');
    })
    .fail(function (errorObj) {
    });
}


function addSPCRelationsResult(result) {
    var plots = result.split(',');
    var msg = '';

    if(result != '')
    {
        if(plots.length == 1)
        {
            msg = result + ' вече съществува в друг договор за преотдаване със съвпадащ период на действие.';
        }
        else if(plots.length > 1)
        {
            msg = result + ' вече съществуват в друг договор за преотдаване със съвпадащ период на действие.';
        }

        jQuery.messager.confirm('Потвърждение', msg + ' Сигурни ли сте, че искате да продължите?', function(r) {
        if (r) {

                TF.Rpc.Subleases
                .SublesePlotsGrid
                .addConfirmedSPCRelation()
                .done(function (dataObj) {
                    jQuery('#sublease-plots-add-tables').datagrid('loadRpc');
                    jQuery('#sublease-plots-tables').datagrid('loadRpc');
                })
                .fail(function (errorObj) {
                });
            }
        });
    }
}

function initEditPlotDataFields() {
    var getChecked = jQuery('#sublease-plots-tables').datagrid('getChecked');

    jQuery('#edit-contract-area').numberbox({
        value: getChecked[0].pc_area,
        min: 0.001,
        precision: 3,
        required: true
    });
    jQuery('#edit-document-area').numberbox({
        value: getChecked[0].document_area,
        min: 0.001,
        precision: 3,
        required: true,
        disabled: true
    });
    jQuery('#edit-rent-area').numberbox({
        value: getChecked[0].rent_area,
        min: 0,
        precision: 3,
    });

    jQuery('#edit-comment').val(getChecked[0].comment);
    jQuery('#win-edit-sublease-plot-data').window('open');
    jQuery('#edit-contract-area').numberbox('validate');
}

function validateEditPlotAreas() {
    if (jQuery('#edit-contract-area').numberbox('isValid')
            && jQuery('#edit-document-area').numberbox('isValid')) {
        var contract_area = parseFloat(jQuery('#edit-contract-area').numberbox('getValue'));
        var document_area = parseFloat(jQuery('#edit-document-area').numberbox('getValue'));
        var rent_area = parseFloat(jQuery('#edit-rent-area').numberbox('getValue'));
        var comment = jQuery('#edit-comment').val();

        if(rent_area > contract_area) {
            jQuery.messager.alert('Грешка', 'Въведената площ по рента е по-голяма от площта по договор на имота.');
            return false;
        }

        if(contract_area <= document_area)
        {
        var getChecked = jQuery('#sublease-plots-tables').datagrid('getChecked');
        var subleaseData = jQuery('#subleases-tree').tree('getSelected');
        let startDate = transformDMYtoYMD(subleaseData.attributes.start_date);
        let dueDate   = transformDMYtoYMD(subleaseData.attributes.due_date);
        let subleaseStartDate = formatYMD(new Date(startDate));
        let subleaseDueDate   = formatYMD(new Date(dueDate));
        let farmingId = subleaseData.attributes.farming_id;

        var obj = new Object();
        obj.sublease_id = subleaseData.id;
        obj.plot_id = getChecked[0].gid;
        obj.contract_area = contract_area;
        obj.rent_area = rent_area;
        obj.comment = comment;
        obj.subleaseStartDate = subleaseStartDate;
        obj.subleaseDueDate = subleaseDueDate;
        obj.farmingId = farmingId;
        obj.currentPlotSubleasedArea = getChecked[0].pc_area;

        TF.Rpc.Subleases
        .SublesePlotsGrid
        .saveEditPlotAreas(obj)
        .done(function (dataObj) {
            jQuery('#sublease-plots-tables').datagrid('loadRpc');
        })
        .fail(function (errorObj) {
            if(errorObj.is(TF.Rpc.ExceptionsList.CONTRACT_AREA_EXCEEDS_PLOT_AREA)) {
                return jQuery.messager.alert('Грешка', TF.Rpc.ExceptionsList.CONTRACT_AREA_EXCEEDS_PLOT_AREA.message, 'error');
            }

            if(errorObj.is(TF.Rpc.ExceptionsList.WRONG_RENT_AREA)) {
                return jQuery.messager.alert('Грешка', TF.Rpc.ExceptionsList.WRONG_RENT_AREA.message, 'error');
            }

            return jQuery.messager.alert('Грешка', 'Възникна грешка при редактиране на имот');
        });

        jQuery('#win-edit-sublease-plot-data').window('close');
        return true;
        }
        else
        {
            jQuery.messager.alert('Грешка', 'Въведената площ по договор е по-голяма от площта по документ на имота.');
            return false;
        }
    } else {
        jQuery.messager.alert('Грешка', 'Моля попълнете задължителните полета.');
        return false;
    }
}

function subleasePlotsAdd() {
    if (!hasPlotRightsRW) {
        messagerPlotsWriteRights();
        return false;
    };
    var subleaseData = jQuery('#subleases-tree').tree('getSelected');

    if (subleaseData) {

        jQuery('#win-plots-add').window('resize', {
            height: getZoomedWindowHeight(530),
            width: 825
        });

        jQuery('#win-plots-add').window('open');
        initSubleasesPlotsAddGrid(subleaseData.id);
    } else {
        jQuery.messager.alert('Грешка', 'Не е избран договор');
    }
}

function subleasePlotsEdit() {
    if (!hasPlotRightsRW) {
        messagerPlotsWriteRights();
        return false;
    };
    var getChecked = jQuery('#sublease-plots-tables').datagrid('getChecked');

    if (getChecked[0]) {
        initEditPlotDataFields();
    } else {
        jQuery.messager.alert('Грешка', 'Моля изберете имот, който искате да редактирате!');
    }
}

function subleasePlotsDelete() {
    if (!hasPlotRightsRW) {
        messagerPlotsWriteRights();
        return false;
    };
    var getChecked = jQuery('#sublease-plots-tables').datagrid('getChecked');
    var subleaseData = jQuery('#subleases-tree').tree('getSelected');

    if (subleaseData) {
        if (getChecked[0]) {
            jQuery.messager.confirm('Потвърждение', 'Сигурни ли сте, че искате да премахнете този имот?', function(r) {
                if (r) {
                    var obj = new Object();
                    obj.plot_id = getChecked[0].gid;
                    obj.sublease_id = subleaseData.id;
                    obj.sublease_num = subleaseData['attributes'].c_num;

                    TF.Rpc.Subleases
                    .SublesePlotsGrid
                    .deleteSPCRelation(obj)
                    .done(function (dataObj) {
                    })
                    .fail(function (errorObj) {
                    });

                    setTimeout(function(){
                        jQuery('#sublease-plots-tables').datagrid('loadRpc');
                        jQuery('#sublease-plots-tables').datagrid('uncheckAll');
                    },1000);

                    
                }
            });
        } else
        {
            jQuery.messager.alert('Грешка', 'Моля изберете имот, който искате да премахнете!');
        }
    } else {
        jQuery.messager.alert('Грешка', 'Не е избран договор!');
    }
}

function subleasePlotsInfo() {
    var getChecked = jQuery('#sublease-plots-tables').datagrid('getChecked');
    if (getChecked[0]) {
        window.open("index.php?page=Plots.Home&plot_id=" + getChecked[0].gid, '_blank');
    } else {
        jQuery.messager.alert('Грешка', 'Моля изберете имот, за който да бъде показана информация.');
    }
}

function subleasePlotsFilter() {
    jQuery('#sublease-plots-tables').datagrid({
        rpcParams: [{
            type: 'view',
            sublease_id: jQuery('#subleases-tree').tree('getSelected').id,
            many_contracts: true
        }]
    });

    var panelElement = jQuery('#sublease-plots-tables').datagrid('getPanel');
    panelElement.panel('setTitle', 'Имоти с повече от едно правно основание');
}

function subleasePlotsClearFilter() {
    jQuery('#sublease-plots-tables').datagrid({
        rpcParams: [{
            type: 'view',
            sublease_id: jQuery('#subleases-tree').tree('getSelected').id,
        }]
    });

    var panelElement = jQuery('#sublease-plots-tables').datagrid('getPanel');
    panelElement.panel('setTitle', 'Имоти');
}

