var copy_sublease_flag = false;
var edit_sublease_flag = false;
var businessStartDate;
var businessEndDate;
jQuery(function () {
    initSearchOnEnter();
    setUserRights();
    TF.Rpc.Common.CombinedComboboxData.read(null, null, {selected: 'current'})
        .done(function (data) {
            ComboboxData = data;
            initComboboxFields();
            initEditPanelValidators();
        })
        .fail(function (error) {
            jQuery.messager.alert('Грешка', error.getMessage(), 'warning');
            return false;
        });
    //retrieve GET parameters
    var GET = {};
    location.search.substr(1).split("&").forEach(function(item) {GET[decodeURIComponent(item.split("=")[0])] = decodeURIComponent(item.split("=")[1])});

    //change URL without refresh if it's possible
    if (history && history.replaceState){
        history.replaceState(null, null, 'index.php?page=' + GET.page);
    }

    initSubleasesTree(1, GET);
    initFilesGrid(0);
    initSubleasesPlotsGrid(0);
    initFarmingContragentsGrid(0);
    jQuery('#btn-add-sublease').bind('click', function() {
        if (!hasPlotRightsRW) {
            messagerPlotsWriteRights();
            return false;
        };
        editSubleaseID = undefined;
        edit_sublease_flag = false;
        copy_sublease_flag = false;
        resetEditSubleaseFields();
        initAddEditPanelValidators();
        addEditRentaField();

        ComboboxData.FarmingYearCombobox.forEach(function (el, index) {
            if(el.selected === true){
                businessStartDate            = ComboboxData.FarmingYearCombobox[index].start_date;
                businessEndDate              = ComboboxData.FarmingYearCombobox[index].end_date;
            }
        });
        jQuery('#sublease-start-date > input').datebox({'editable': true});
        jQuery('#sublease-due-date > input').datebox({'editable': true});
        jQuery('#sublease-start-date > input').datebox({'disabled': false});
        jQuery('#sublease-due-date > input').datebox({'disabled': false});
        
        jQuery('#sublease-start-date > input').datebox('setValue', businessStartDate);
        jQuery('#sublease-due-date > input').datebox('setValue', businessEndDate);

        jQuery('#win-add-edit-subleases').window('resize', {
            height: getZoomedWindowHeight(660),
            width: 480
        });

        jQuery('#win-add-edit-subleases').window('open');

        return false;
    });

    jQuery('#btn-edit-sublease').bind('click', function() {
        if (!hasPlotRightsRW) {
            messagerPlotsWriteRights();
            return false;
        };
        var subleaseData = jQuery('#subleases-tree').tree('getSelected');

        if (subleaseData) {
            editSubleaseID = subleaseData.id;
            TF.Rpc.Subleases.SubleasesTree.preEdit(subleaseData.id)
            .done(function (data) {
                edit_sublease_flag = true;
                copy_sublease_flag = false;
                preEditResult(data);
            });
        } else {
            jQuery.messager.alert('Грешка', 'Не е избран договор!');
        }
        return false;
    });

    jQuery('#btn-delete-sublease').bind('click', function() {
        if (!hasPlotRightsRW) {
            messagerPlotsWriteRights();
            return false;
        };
        var subleaseData = jQuery('#subleases-tree').tree('getSelected');

        if (subleaseData) {
            jQuery.messager.confirm('Потвърждение', 'Сигурни ли сте, че искате да премахнете този договор?', function(r) {
                if (r) {
                    editSubleaseID = undefined;
                    TF.Rpc.Subleases.SubleasesTree.deleteSublease(subleaseData.id)
                    .done(function (data) {
                        jQuery('#subleases-tree').tree('loadRpc');
                    });
                }
            });
        } else {
            jQuery.messager.alert('Грешка', 'Не е избран договор!');
        }
        return false;
    });

    jQuery('#btn-filter-subleases').bind('click', function(){
        jQuery('#win-filter-subleases').window('resize', {
            height: getZoomedWindowHeight(660),
            width: 600
        });

        jQuery('#win-filter-subleases').window('open');
        return false;
    });

    jQuery('#btn-filter-subleases-tree').bind('click', function() {
        var obj = {};
        obj.c_num = jQuery('#search-sublease > input').val();
        obj.c_status = jQuery('#search-sublease-status').combobox('getValue');
        obj.farming_year = jQuery('#search-farming-year').combobox('getValue');
        obj.renta_types = jQuery('#search-renta-types').combobox('getValues');
        obj.date_from = jQuery('#search-date-from > input').datebox('getValue');
        obj.date_to = jQuery('#search-date-to > input').datebox('getValue');
        obj.due_date_from = jQuery('#search-due-date-from > input').datebox('getValue');
        obj.due_date_to = jQuery('#search-due-date-to > input').datebox('getValue');
        obj.farming = jQuery('#search-farming > input').combobox('getValues');
        obj.c_type = jQuery('#search-type > input').combobox('getValues');
        obj.kad_ident = jQuery('#search-kad-ident').val();
        obj.ekate = jQuery('#search-ekatte').combobox('getValues');
        obj.masiv = jQuery('#search-masiv').val();
        obj.number = jQuery('#search-number').val();
        obj.category = jQuery('#search-category').combobox('getValues');
        obj.area_type = jQuery('#search-area-type').combobox('getValues');
        obj.irrigated_area = jQuery('#search-irrigated-area').combobox('getValue');
        obj.subleaser_name = jQuery('#search-subleaser-name').val();
        obj.subleaser_egn = jQuery('#search-subleaser-egn').val();
        obj.rep_name = jQuery('#search-represent-name').val();
        obj.rep_egn = jQuery('#search-represent-egn').val();
        obj.company_name = jQuery('#search-company-name').val();
        obj.company_eik = jQuery('#search-company-eik').val();
        obj.person_name = jQuery('#search-person-name').val();
        obj.person_egn = jQuery('#search-person-egn').val();

        obj.c_num_complete_match = false;
        if (jQuery('#search-sublease-complete-match').is(':checked'))
        {
            obj.c_num_complete_match = true;
        }

        //reinit tree with query params
        initSubleasesTree(1, obj);
        jQuery('#win-filter-subleases').window('close');
    });

    jQuery('#btn-clear-filter-subleases').bind('click', function() {
        jQuery('#search-sublease > input').val('');
        jQuery('#search-sublease-status').combobox('reset');
        jQuery('#search-farming-year').combobox('reset');
        jQuery('#search-renta-types').combobox('reset');
        jQuery('#search-date-from > input').datebox('reset');
        jQuery('#search-date-to > input').datebox('reset');
        jQuery('#search-due-date-from > input').datebox('reset');
        jQuery('#search-due-date-to > input').datebox('reset');
        jQuery('#search-farming > input').combobox('reset');
        jQuery('#search-type > input').combobox('reset');
        jQuery('#search-kad-ident').val('');
        jQuery('#search-ekatte').combobox('reset');
        jQuery('#search-masiv').val('');
        jQuery('#search-number').val('');
        jQuery('#search-category').combobox('reset');
        jQuery('#search-area-type').combobox('reset');
        jQuery('#search-irrigated-area').combobox('loadRpc');
        jQuery('#search-subleaser-name').val('');
        jQuery('#search-subleaser-egn').val('');
        jQuery('#search-represent-name').val('');
        jQuery('#search-represent-egn').val('');
        jQuery('#search-company-name').val('');
        jQuery('#search-company-eik').val('');
        jQuery('#search-person-name').val('');
        jQuery('#search-person-egn').val('');

        //reinit tree with query params
        initSubleasesTree();

        return false;
    });

    jQuery('#btn-change-sublease-status').bind('click', function() {
        if (!hasPlotRightsRW) {
            messagerPlotsWriteRights();
            return false;
        };
        var getSelected = jQuery('#subleases-tree').tree('getSelected');
        var comment = '';
        var alert_text = '';
        var obj = new Object;

        if (getSelected) {
            if(getSelected.attributes.comment != '-') {
                comment = getSelected.attributes.comment;
            }

            if (getSelected.attributes.active == 0) {
                alert_text = 'Сигурни ли сте, че искате да маркирате този договор като действащ?';
                obj.status = true;
            } else {
                alert_text = 'Сигурни ли сте, че искате да маркирате този договор като анулиран?';
                obj.status = false;
            }

            alert_text += '<table width="100%" cellspacing="0" cellpadding="0" style="margin-top:10px;">';
            alert_text += '<tr>';
            alert_text += '<td>Забележка</td>';
            alert_text += '<td style="padding-left: 10px;">';
            alert_text += '<textarea id="sublease-status-comment" style="resize: none; width:190px; height:60px;">' + comment + '</textarea>';
            alert_text += '</td>';
            alert_text += '</tr>';
            alert_text += '</table>';

            jQuery.messager.confirm('Потвърждение', alert_text, function(r) {
                if (r) {
                    editSubleaseID = getSelected.id;
                    obj.id = getSelected.id;
                    obj.comment = jQuery('#sublease-status-comment').val();

                    TF.Rpc.Subleases.SubleasesTree.changeActiveStatus(obj)
                    .done(function (data) {
                        jQuery('#subleases-tree').tree('loadRpc');
                    });
                }
            });
        } else {
            jQuery.messager.alert('Грешка', 'Моля изберете договор, който искате да анулирате!');
        }
        return false;
    });

    jQuery('#btn-print-sublease').bind('click', function() {
        var getSelected = jQuery('#subleases-tree').tree('getSelected');

        if (getSelected) {
            if (getSelected.attributes.c_type != 1 && getSelected.attributes.c_type != 4)
            {
                initPrintSubleaseTemplatesGrid(getSelected.id);
                jQuery('#win-contracts-templates').window('open');
            } else {
                jQuery.messager.alert('Грешка', 'Договори от тип собственост или споразумение не могат да бъдат разпечатвани!');
            }
        } else {
            jQuery.messager.alert('Грешка', 'Моля изберете договор!');
        }
        return false;
    });


    jQuery('#btn-print-filtered-contracts').bind('click', function () {
        var contracts = jQuery('#subleases-tree').tree('getRoots');
        var contractsData = [];
        let page = jQuery('#subleases-tree').tree('options')['page'];
        if(contracts.length === 0) {
            jQuery.messager.alert('Грешка', 'Нама намерени договори за принтиране');
            return false;
        }
    
        for(let contract of contracts) {
            contractsData.push({
                'id': contract.id,
                'c_num': contract.attributes.c_num,
            });
        }

        initPrintSubleaseTemplatesGrid(null, contractsData, page);
        jQuery('#win-contracts-templates').window('open');
    });



    jQuery('#btn-copy-sublease').bind('click', function () {
        if (!hasPlotRightsRW) {
            messagerPlotsWriteRights();
            return false;
        }
        var selected = jQuery('#subleases-tree').tree('getSelected');

        if (!selected) {
            jQuery.messager.alert('Грешка', 'Не е избран договор!');
            return false;
        }

        //No Rights to operate with "Договори за собственост"
        if(!hasContractsOwnWriteRights) {
            messagerContractsOwnWriteRights();
            return false;
        }

        if (selected.attributes.from_sublease > 0) {
            messageContractIsFromSublease(selected);
            return false;
        }

        try {
            initCopySubleasePlots(true);
        } catch (error) {
            if (typeof error.is === 'function' && error.is(TF.Rpc.ExceptionsList.INVALID_USER_CONTRACTS_RIGHTS_OWN_CONTRACTS)) {
                messagerContractsOwnWriteRights();
            }
        }
    });
    
    //init main filter fields
    jQuery('#search-date-from  > input').datebox();
    jQuery('#search-date-to  > input').datebox();
    jQuery('#search-due-date-from  > input').datebox();
    jQuery('#search-due-date-to  > input').datebox();
});

/**
 * Load selected sublease data and request available plots to sublease for new period
 * 
 * @param bool recalculateDates
 */
initCopySubleasePlots = (recalculateDates = true) => {
    clearCopySubleaseFields();
    jQuery('#win-copy-sublease').window('open');

    const selected = jQuery('#subleases-tree').tree('getSelected');
    let subleasesPlotsGrid = jQuery('#sublease-plots-tables');

    if (recalculateDates === true) {
        let dates     = calculateSubleasePeriod();
        var startDate = dates.startDate;
        var dueDate   = dates.dueDate;  
    } else {        
        var startDate = jQuery('#copy-sublease-start-date > input').datebox('getValue'); 
        var dueDate = jQuery('#copy-sublease-due-date > input').datebox('getValue');  
    }

    if(startDate >= dueDate) {
        return jQuery.messager.alert('Грешка', 'Крайната дата не може да е по-малка от датата на влизане в сила!', 'warning');
    }

    const requestParamsObj = {
        sublease_id       : selected.id,
        maint_contract_id : selected.attributes.maint_contract_id,
        start_date        : startDate,
        due_date          : dueDate,
        farming_id        : selected.attributes.farming_id
    };

    let subleasePlotsTables = jQuery('#sublease-plots-table');
    subleasePlotsTables.treegrid({
        iconCls: 'icon-edit-geometry',
        nowrap: true,
        title: 'Имоти',
        autoRowHeight: true,
        striped: true,
        animate: true,
        pageSize: 10,
        fit: true,
        singleSelect: true,
        selectOnCheck: false,
        checkOnSelect: false,
        fitColumns: true,
        showFooter: false,
        url: 'index.php?subleases-rpc=subleases-tree',
        rpcMethod: 'loadSublease',
        rpcParams: [requestParamsObj],
        sortName: 'kad_ident',
        sortOrder: 'asc',
        idField: 'index',
        treeField: 'kad_ident',
        pagination: false,
        rownumbers: true,
        rowStyler: function (row) {
            if (!row.is_checkable) {
                return 'color: #897f7f;background:#f7f7f7';
            }
        },
        frozenColumns: [[
            {
                field: 'ck',
                checkbox: true,
                styler: function (value, row) {
                    if (!row.is_checkable) {
                        return 'visibility: hidden;';
                    }
                }
            }]],
        columns: [[
            {
                field: 'kad_ident',
                title: '<b>Идентификатор</b>',
                sortable: true,
                width: 200
            }, {
                field: 'ekatte_name',
                title: '<b>Землище</b>',
                sortable: true,
                width: 100,
            }, {
                field: 'category',
                title: '<b>Категория</b>',
                sortable: true,
                width: 100
            }, {
                field: 'area_type',
                title: '<b>НТП</b>',
                sortable: true,
                width: 100
            }, {
                field: 'contract_area',
                title: '<b>Площ по<br/>договор (дка)</b>',
                sortable: true,
                halign: 'center',
                width: 100,
                editor: {
                    type: 'numberbox',
                    options: {
                        required: true,
                        missingMessage: 'Полето е задължително',
                        precision: 3
                    }
                }
            }, {
                field: 'document_area',
                title: '<b>Площ по<br/>документ (дка)</b>',
                sortable: true,
                halign: 'center',
                width: 100,
                editor: {
                    type: 'numberbox',
                    options: {
                        required: true,
                        missingMessage: 'Полето е задължително',
                        precision: 3
                    }
                }
            }, {
                field: 'rent_area',
                title: '<b>Площ за<br/>рента (дка)</b>',
                sortable: true,
                halign: 'center',
                width: 100,
                editor: {
                    type: 'numberbox',
                    options: {
                        required: true,
                        missingMessage: 'Полето е задължително',
                        precision: 3
                    }
                }
            },
            {
                field: 'action',
                title: '',
                sortable: false,
                halign: 'center',
                width: 50,
                formatter: function (value, row) {
                    if (!row.hasOwnProperty('gid')) {
                        return;
                    }
                    
                    return '<a onclick="editCopySubleasePlotData(' + row.index + ' , ' + row.is_checkable + ');" class="easyui-linkbutton l-btn l-btn-small l-btn-plain" group=""><span class="l-btn-left l-btn-icon-left"><span class="l-btn-text"></span><span class="l-btn-icon icon-edit">&nbsp;</span></span></a>';
                }
            }
        ]],
        onBeforeCheck: function (row) {
            if (!row.hasOwnProperty("is_checkable")) {
                return;
            }
            return row.is_checkable;
        },
        onDblClickRow: function (row) {
            if (!row.hasOwnProperty("index") || row.index < 0) {
                return;
            }
            var editors = jQuery(this).treegrid("getEditors", row.index);
            var hasEditor = editors.length > 0;
            var mode = hasEditor ?  "endEdit" :"beginEdit";
            jQuery(this).treegrid(mode, row.index);
        },
        onLoadSuccess: function (row, data) {
            setCopySubleaseLoadedData(startDate, dueDate);
            jQuery(this).treegrid('clearChecked');
            jQuery(this).treegrid('clearSelections');
            jQuery(this).treegrid('uncheckAll');
            jQuery(this).treegrid('checkAll');
        },
        loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
        loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
    });

    jQuery('#copy-sublease-start-date > input').on('change', function () {
        initCopySubleasePlots(false);
    });

    jQuery('#copy-sublease-due-date > input').on('change', function () {
        initCopySubleasePlots(false);
    });
}

/**
 * 
 * Process copy sublease
 */
copySublease = () => {
    if (validateCopySublease() == false) {
        return;
    }    

    const selectedSublease  = jQuery('#subleases-tree').tree('getSelected');
    let subleasePlotsTable  = jQuery('#sublease-plots-table');
    const selectedPlotsData = subleasePlotsTable.treegrid('getChecked');
    
    const startDate   = jQuery('#copy-sublease-start-date > input').datebox('getValue');
    const dueDate     = jQuery('#copy-sublease-due-date > input').datebox('getValue');
    const date        = jQuery('#copy-sublease-date > input').datebox('getValue');
    const subleaseNum = jQuery('#copy-sublease-number > input').val();
    const is_declaration_subleased = jQuery('#copy-contract-is-subleased').is(':checked');

    let addedPlotsData = [];
    for (var i = 0; i < selectedPlotsData.length; i++) {
        var row = selectedPlotsData[i];
        subleasePlotsTable.treegrid('endEdit', row.index);
        var contractAreaEditor = subleasePlotsTable.treegrid('getEditor', {id: row.index, field: 'contract_area'});
        var documentAreaEditor = subleasePlotsTable.treegrid('getEditor', {id: row.index, field: 'document_area'});
        if (contractAreaEditor || documentAreaEditor) {
            return jQuery.messager.alert('Грешка', 'Моля попълнете всички задължителни полета!');
        }

        let children = subleasePlotsTable.treegrid('getChildren', row.index);

        for (var j = 0; j < children.length; j++) {
            subleasePlotsTable.treegrid('endEdit', children[j].index);
        }
        if (!row.is_checkable) {
            continue;
        }

        let plotObj           = {};
        plotObj.gid           = row.gid;
        plotObj.contract_area = row.contract_area;
        plotObj.document_area = row.document_area;
        plotObj.rent_area     = row.rent_area;
        plotObj.pc_rel_id     = row.pc_rel_id;
        plotObj.pc_rel_id_agg = row.pc_rel_id_agg;
        addedPlotsData.push(plotObj);
    }

    let requestObject = {
        'copy_contract_id': selectedSublease.attributes.contract_id,
        'c_num'           : subleaseNum,
        'c_date'          : date,
        'nm_usage_rights' : selectedSublease.attributes.nm_usage_rights,
        'sv_num'          : selectedSublease.attributes.sv_num,
        'start_date'      : startDate,
        'due_date'        : dueDate,
        'sublease_date'   : date,
        'farming_id'      : selectedSublease.attributes.farming_id,
        'renta'           : selectedSublease.attributes.renta,
        'comment'         : selectedSublease.attributes.comment,
        'is_sublease'     : true,
        'plots'           : addedPlotsData,
        'is_declaration_subleased' : is_declaration_subleased
    }

    TF.Rpc.Subleases.SubleasesTree.copySublease(requestObject)
        .done(function (data) {
            jQuery('#win-copy-sublease').window('close');
            jQuery('#subleases-tree').tree('loadRpc');
        })
        .fail(function (error) {
            jQuery.messager.alert('Грешка', error.getMessage(), 'warning');
        });
}

/**
 * 
 * @param {*} index 
 * @param {*} is_checkable 
 * @returns bool
 */
function editCopySubleasePlotData(index , is_checkable) {
	if(index < 0 || !is_checkable){
		return false;
	}
    var editors = jQuery("#sublease-plots-table").treegrid("getEditors", index);
	var inEditMode = editors.length > 0;
	var mode = "beginEdit";
	if (inEditMode) {
		mode = "endEdit";
	}
	jQuery("#sublease-plots-table").treegrid(mode, index);
	return false;
}

/**
 * 
 * Validates copy sublease processed data
 */
validateCopySublease = () => {
    if (jQuery('#copy-sublease-date > input').datebox('getValue') != '' 
        && jQuery('#copy-sublease-start-date  > input').datebox('getValue') != ''
        && jQuery('#copy-sublease-due-date > input').datebox('getValue') != ''
    )
    {
        if (!compareDates('#copy-sublease-start-date > input', '#copy-sublease-due-date > input', 2)) {
            jQuery.messager.alert('Грешка', 'Въведена е грешна дата - крайната дата е по-ранна от датата на влизане в сила на договора.', 'warning');
            return false;
        }
    } else {
        jQuery.messager.alert('Грешка', 'Моля попълнете всички задължителни полета.');
        return false;
    }

    let subleasePlotsTable  = jQuery('#sublease-plots-table');
    const selectedPlotsData = subleasePlotsTable.treegrid('getChecked');

    if(selectedPlotsData.length == 0) {
        jQuery.messager.alert('Грешка', 'Моля изберете имот за копиране.');      
        return false;
    }

    return true;
}

/** 
 *   
 *  Return number of days between d0 and d1.
 *  Returns positive if d0 < d1, otherwise negative.
 *
 *  @param {Date} d0  - start date
 *  @param {Date} d1  - end date
 *  @returns {number} - whole number of days between d0 and d1
 *
 */
daysDifference = (d0, d1) => {
    var diff = new Date(d1).setHours(12) - new Date(d0).setHours(12);

    return Math.round(diff/8.64e7);
}

/**
 * 
 * Returns d.m.Y to Y.m.d
 */
transformDMYtoYMD = (date) => {
    const datePartsArr = date.split('.');
    
    return datePartsArr[2]+'.'+datePartsArr[1]+'.'+datePartsArr[0];
}

/**
 * 
 * Return fomatted m.d.Y date
 */
formatDMY = (date) => {
    return date.getDate() +'.'+ (date.getMonth() + 1) +'.'+ date.getFullYear() 
}

/**
 * Return formatted Y-m-d
 * 
 */
formatYMD = (date) => {
    return date.getFullYear() +'-'+ (date.getMonth() + 1) +'-'+ date.getDate() 
}


/**
 * 
 * Return dates of selected contract increased by diff in period between them
 */  
calculateSubleasePeriod = () => {
    const selected = jQuery('#subleases-tree').tree('getSelected');

    let startDate = transformDMYtoYMD(selected.attributes.start_date);
    let dueDate   = transformDMYtoYMD(selected.attributes.due_date);
  
    let subleaseStartDate = new Date(startDate);
    let subleaseDueDate   = new Date(dueDate);

    const diffInDays =  daysDifference(startDate, dueDate) + 1;
    
    subleaseStartDate.setDate(subleaseStartDate.getDate() + diffInDays);
    subleaseDueDate.setDate(subleaseDueDate.getDate() + diffInDays);

    const newStartDate = subleaseStartDate.getFullYear().toString().concat('-10-01');
    const newDueDate = subleaseDueDate.getFullYear().toString().concat('-09-30');

    return {
        'startDate' : newStartDate,
        'dueDate'   : newDueDate
    };
}

/**
 * 
 * Clear previously loaded sublease data
 */
clearCopySubleaseFields = () => {
    jQuery('#copy-sublease-number > input').val('');
    jQuery('#copy-sublease-contract-type').val('');
    jQuery('#copy-sublease-farming').val('');
    jQuery('#copy-sublease-owner').val('');
    jQuery('#copy-contract-is-subleased').prop('checked', false);
}

/**
 * 
 * Fill copy sublease data
 */
setCopySubleaseLoadedData = (startDate, dueDate) => {
    const selected = jQuery('#subleases-tree').tree('getSelected');
    jQuery('#copy-sublease-number > input').val(selected.attributes.c_num);

    jQuery('#copy-sublease-date > input').datebox({
        value: new Date()
    });

    jQuery('#copy-sublease-start-date > input').datebox({
        value: startDate,
        editable: false,
        onSelect: function(date){
            jQuery('#copy-sublease-start-date > input').datebox('setValue',formatYMD(date));
        	initCopySubleasePlots(false);
        }
    });

    jQuery('#copy-sublease-due-date > input').datebox({
        value: dueDate,
        editable: false,
        onSelect: function(date){
            jQuery('#copy-sublease-due-date > input').datebox('setValue',formatYMD(date));
        	initCopySubleasePlots(false);
        }
    });

    jQuery('#copy-sublease-contract-type').val(selected.attributes.nm_usage_rights);
    jQuery('#copy-sublease-farming').val(selected.attributes.farming);
    jQuery('#copy-contract-is-subleased').prop('checked', selected.attributes.is_declaration_subleased);

    const fullName = (selected.attributes.rep_name ? selected.attributes.rep_name : '-') + ' ' + (selected.attributes.rep_surname ? selected.attributes.rep_surname : '-') + ' ' + (selected.attributes.rep_lastname ? selected.attributes.rep_lastname : '-');
    let gridData = jQuery('#farming-contragents-tables').datagrid('getData');
    let subleasesContragentsTable = jQuery('#sublease-contragents-tables').datagrid('getData');

    let owner = '- - -';
    if (parseInt(gridData.total) > 0) {
        owner = gridData.rows[0].farming;
    }

    if (subleasesContragentsTable.total > 0) {
        owner = '';

        subleasesContragentsTable.rows.forEach(function(element, index) {
            owner += element.owner_names;
        })
    }

    jQuery('#copy-sublease-owner').val(
        owner
    );
}

function initAddEditPanelValidators() {
    var date = new Date(),
        todayDate = date.getFullYear() + '-' + (date.getMonth() + 1) + '-' + date.getDate(),
        subleasedContractTypeComboboxData = ComboboxData.SubleasedContractType,
        farmingComboboxData               = ComboboxData.FarmingCombobox;

    jQuery('#sublease-number > input').validatebox({
        required: true,
        missingMessage: 'Моля въведете номер на договор.'
    });

    jQuery('#sublease-date > input').datebox({
        required: true,
        missingMessage: 'Моля изберете дата на договор.',
        value: todayDate
    });

    jQuery('#sublease-start-date > input').datebox({
        required: true,
        missingMessage: 'Моля изберете дата на влизане в сила да договор.',
        value: todayDate
    });

    jQuery('#sv-date > input').datebox({});

    var daySpinner = jQuery('#pd-day > input');
    jQuery('#pd-month > input').combobox({
        data: months,
        valueField: 'value',
        textField: 'label',
        onLoadSuccess: function() {
            daySpinner.numberspinner({
                min: 1,
                max: 31,
                precision: 0,
            });
        },
        onSelect: function(rec){
            var day = daySpinner.numberspinner('getValue');
            if(day > rec.maxDays) {
                day = rec.maxDays;
            }
            daySpinner.numberspinner({
                min: 1,
                max: rec.maxDays,
                precision: 0,
                value:day,
            });
        }
    });

    jQuery('#sublease-due-date > input').datebox({
        required: true,
        missingMessage: 'Моля въведете продължителност на договор в месеци.',
        value: todayDate
    });

    jQuery('#renta > input').numberspinner({
        required: true,
        precision: 2,
        min: 0,
        value: 0,
        missingMessage: 'Моля въведете рента в лева на декар.'
    });

    subleasedContractTypeComboboxData[0].selected = true;
    jQuery('#sublease-type > input').combobox({
        data: subleasedContractTypeComboboxData,
        valueField: 'id',
        textField: 'name',
        loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
        loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
    });

    let userFarmingPermissions = ComboboxData.UserFarmingPermissions;
    let selectedOptionId = 0;
    let newFarmingComboboxData = [];

    farmingComboboxData.forEach(function (el) {
        if (el.id !== "") {
            el.selected = false;
            let hasPermission = Object.values(userFarmingPermissions).includes(el.id);
            if(!hasPermission) {   
                el.disabled = true;
            } else {
                if (selectedOptionId === 0) {
                    selectedOptionId = el.id;
                    el.selected = true;
                }
            }
            newFarmingComboboxData.push(el);
        }
    });
    jQuery('#sublease-farming > input').combobox({
        data: newFarmingComboboxData,
        valueField: 'id',
        textField: 'name',
        disabled: false,
        onLoadSuccess: function() {
           
        },
        loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
        loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
    });
    jQuery('#add-new-renta-btn').linkbutton({
        iconCls: 'icon-add',
        width:26
    });

    jQuery('#remove-renta-btn').linkbutton({
        iconCls: 'icon-cancel',
        width:26
    });

    jQuery('#renta-error-win').window({
        width:275,
        height:150,
        modal:true,
        closed:true,
        resizable: false,
        collapsible: false,
        minimizable: false,
        maximizable: false,
        title: 'Грешка при избор на тип на рента.'
    });
}

function initEditPanelValidators() {
    var date = new Date();
    var todayDate = date.getFullYear() + '-' + (date.getMonth() + 1) + '-' + date.getDate();


    jQuery('#sublease-number > input').validatebox({
        required: true,
        missingMessage: 'Моля въведете номер на договор.'
    });

    jQuery('#sublease-date > input').datebox({
        required: true,
        missingMessage: 'Моля изберете дата на договор.'
    });

    jQuery('#sublease-start-date > input').datebox({
        required: true,
        missingMessage: 'Моля изберете дата на влизане в сила да договор.'
    });

    jQuery('#sv-date > input').datebox({});

    var daySpinner = jQuery('#pd-day > input');
    jQuery('#pd-month > input').combobox({
        data: months,
        valueField: 'value',
        textField: 'label',
        onLoadSuccess: function() {
            daySpinner.numberspinner({
                min: 1,
                max: 31,
                precision: 0,
            });
        },
        onSelect: function(rec){
            var day = daySpinner.numberspinner('getValue');
            var selection = jQuery('#pd-month > input').combobox('getValue');
            if(day > rec.maxDays) {
                day = rec.maxDays;
            }
            daySpinner.numberspinner({
                min: 1,
                max: rec.maxDays,
                precision: 0,
                value:day,
            });
        }
    });

    jQuery('#sublease-due-date > input').datebox({
        required: true,
        missingMessage: 'Моля въведете продължителност на договор в месеци.'
    });

    jQuery('#renta > input').numberspinner({
        required: true,
        precision: 2,
        min: 0,
        missingMessage: 'Моля въведете рента в лева на декар.'
    });

    jQuery('#sublease-type > input').combobox({
        data: ComboboxData.SubleasedContractType,
        valueField: 'id',
        textField: 'name',
        loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
        loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
    });

    let userFarmingPermissions = ComboboxData.UserFarmingPermissions;
    newFarmingComboboxData = [];
    ComboboxData.FarmingCombobox.forEach(function (el) {
        if (el.id !== "") {
            el.selected = false;
            let hasPermission = Object.values(userFarmingPermissions).includes(el.id);
            if(!hasPermission) {   
                el.disabled = true;
            } 
            newFarmingComboboxData.push(el);
        }    
    });
    jQuery('#sublease-farming > input').combobox({
        data: newFarmingComboboxData,
        valueField: 'id',
        textField: 'name',
        onLoadSuccess: function() {
        },
        loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
        loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
    });

    jQuery('#add-new-renta-btn').linkbutton({
        iconCls: 'icon-add',
        width:26
    });

    jQuery('#remove-renta-btn').linkbutton({
        iconCls: 'icon-cancel',
        width:26
    });

    jQuery('#renta-error-win').window({
        width:275,
        height:150,
        modal:true,
        closed:true,
        resizable: false,
        collapsible: false,
        minimizable: false,
        maximizable: false,
        title: 'Грешка при избор на тип на рента.'
    });
}

function preEditResult(rentaResult) {
    var subleaseData = jQuery('#subleases-tree').tree('getSelected');
    if (subleaseData) {
        TF.Rpc.Subleases.SubleasesTree.markSubleaseForEdit(subleaseData.id)
        .done(function (data) {
            resetEditSubleaseFields();
            setEditSubleaseFieldsData(data);
            jQuery('#win-add-edit-subleases').window('open');
        })
        .fail(function (error) {
            jQuery.messager.alert('Грешка', error.getMessage(), 'warning');
        });;
    }
    else {
        jQuery.messager.alert('Грешка', 'Не е избран договор!');
    }
    return false;
}

function validateSaveSublease() {
    var typeID = jQuery('#sublease-type > input').combobox('getValue');

    if (typeID) {
        if (jQuery('#sublease-number > input').val() != ''
                && jQuery('#sublease-date > input').datebox('getValue') != ''
                && jQuery('#sublease-start-date > input').datebox('getValue') != ''
                && jQuery('#sublease-farming > input').combobox('getValue') != ''
                && jQuery('#sublease-due-date > input').datebox('getValue') != ''
                && jQuery('#renta input').numberspinner('getValue'))
        {
            if (!compareDates('#sublease-start-date > input', '#sublease-due-date > input', 2)) {
                jQuery.messager.alert('Грешка', 'Въведена е грешна дата - крайната дата е по-ранна от датата на влизане в сила на договора.', 'warning');
                return false;
            }
            checkForExistance();
        } else {
            jQuery.messager.alert('Грешка', 'Моля попълнете всички задължителни полета.');
        }
    }
}

function checkForExistance() {
    var obj = getAddEditSubleaseFields();

    TF.Rpc.Subleases.SubleasesTree.checkForExistence(obj)
    .done(function (data) {
        checkForExistenceResult(data);
        jQuery('#subleases-tree').tree('loadRpc');
    })
    .fail(function (error) {
        if (error.is(TF.Rpc.ExceptionsList.CANNOT_MODIFY_FROM_SUBLEASE_CONTRACT_DATES)) {
            jQuery.messager.alert('Грешка', TF.Rpc.ExceptionsList.CANNOT_MODIFY_FROM_SUBLEASE_CONTRACT_DATES.message, 'error');
        }
    });;
}

function getAddEditSubleaseFields() {
    var subleaseData = jQuery('#subleases-tree').tree('getSelected');

    var obj = {};
    if(copy_sublease_flag == true){
        obj.sublease_id = 0;
        obj.copy_sublease_id = subleaseData.id;
        copy_sublease_flag = false;
    }else{
        if(edit_sublease_flag == true){
            obj.sublease_id = subleaseData.id;
            obj.copy_sublease_id = 0;
        } else{
            edit_sublease_flag = false;
        }
    }

    obj.c_num = jQuery('#sublease-number > input').val();
    obj.farming_id = jQuery('#sublease-farming > input').combobox('getValue');
    obj.nm_usage_rights = jQuery('#sublease-type > input').combobox('getValue');
    obj.sv_num = jQuery('#sv-num > input').val();
    obj.sv_date = jQuery('#sv-date > input').datebox('getValue');
    obj.start_date = jQuery('#sublease-start-date > input').datebox('getValue');
    obj.due_date = jQuery('#sublease-due-date > input').datebox('getValue');
    obj.c_date = jQuery('#sublease-date > input').datebox('getValue');
    obj.renta = jQuery('#renta > input').val();
    obj.pd_day = jQuery('#pd-day > input').val();
    obj.pd_month = jQuery('#pd-month > input').combobox('getValue');
    obj.comment = jQuery('#sublease-comment > textarea').val();
    obj.is_declaration_subleased = jQuery('#contract-is-subleased').is(':checked');

    obj.additionalRentas = [];
    for (var i = 0; i < jQuery('.js-renta-type-row').length; i++) {
        obj.additionalRentas[i]= {
            type:parseInt(jQuery('#renta-type-cb-' + i).combobox('getValue')),
            value:parseFloat(jQuery('#renta-value-' + i)[0].value)
        }
    }

    return obj;
}

function checkForExistenceResult(data) {
    if (data) {
        jQuery.messager.confirm('Потвърждение', 'Вече съществува договор за преотдаване с такъв номер! Сигурни ли сте, че искате да продължите?', function(r) {
            if (r) {

                var obj = getAddEditSubleaseFields();
                TF.Rpc.Subleases.SubleasesTree.saveSublease(obj)
                .done(function (data) {
                    jQuery('#win-add-edit-subleases').window('close');
                    jQuery('#subleases-tree').tree('loadRpc');
                })
                .fail(function (error) {
                    if (error.is(TF.Rpc.ExceptionsList.CANNOT_MODIFY_FROM_SUBLEASE_CONTRACT_DATES)) {
                        jQuery.messager.alert('Грешка', TF.Rpc.ExceptionsList.CANNOT_MODIFY_FROM_SUBLEASE_CONTRACT_DATES.message, 'error');
                    }
                });
            }
        });
    }
    else {
        jQuery('#win-add-edit-subleases').window('close');
        jQuery('#subleases-tree').tree('loadRpc');
    }
}

function initPrintSubleaseTemplatesGrid(sublease_id, contractsData = [], page = 1) {
    jQuery('#templates-tables').datagrid({
        iconCls: 'icon-template',
        nowrap: true,
        autoRowHeight: true,
        striped: true,
        pageSize: 50,
        fit: true,
        fitColumns: true,
        showFooter: true,
        url: 'index.php?common-rpc=templates-grid',
        sortName: 'id',
        rpcParams: [{}],
        sortOrder: 'asc',
        border: false,
        idField: 'id',
        singleSelect: true,
        frozenColumns: [[
                {
                    field: 'ck',
                    checkbox: true
                }
            ]],
        columns: [[
                {
                    field: 'title',
                    title: '<b>Име</b>',
                    sortable: true,
                    width: 150
                }, {
                    field: 'add_date',
                    title: '<b>Дата на добавяне</b>',
                    sortable: true,
                    width: 150
                }
                ]],
                pagination: true,
                rownumbers: true,
                toolbar: [
                {
                    id: 'btnexportpdfblank',
                    text: 'Отпечатай pdf',
                    iconCls: 'icon-pdf',
                    handler: function() {
                        var getChecked = jQuery('#templates-tables').datagrid('getChecked');
                        if (getChecked[0]) {
                            var obj = {
                                template_id: getChecked[0].id,
                                sublease_id: sublease_id,
                                contractsData: contractsData,
                                blank_type: 'pdf'
                            };
                            if(contractsData && contractsData.length > 0) {
                                jQuery.messager.confirm('Потвърждение', 'Страница ' + page + ' ще бъде разпечатана. Сигурни ли сте, че искате да продължите?', function (r) {
                                    if (r) {
                                        TF.Rpc.Subleases.SubleasesExports.exportContractBlank(obj)
                                            .done(function (data) {
                                                createSubleasesDownloadVariables();
                                                winDownloadContract.window('open');
                                                _pathFile = data.file_path;
                                                _fileName = data.file_name;
                                                downloadFileContract.attr("href", _pathFile);
                                            })
                                            .fail(function (errorObj) {});
                                    }
                                });
                            } else {
                                TF.Rpc.Subleases.SubleasesExports.exportContractBlank(obj)
                                    .done(function (data) {
                                        createSubleasesDownloadVariables();
                                        winDownloadContract.window('open');
                                        _pathFile = data.file_path;
                                        _fileName = data.file_name;
                                        downloadFileContract.attr("href", _pathFile);
                                    })
                                    .fail(function (errorObj) {});
                            }

                        } else {
                            jQuery.messager.alert('Грешка', 'Моля изберете бланка, върху която да разпечатате!');
                        }
                    }
                },{
                    id: 'btnexportdocblank',
                    text: 'Отпечатай Word',
                    iconCls: 'icon-word',
                    handler:function() {
                        var getChecked = jQuery('#templates-tables').datagrid('getChecked');

                        if (getChecked[0]) {
                            var obj = {
                                template_id: getChecked[0].id,
                                sublease_id: sublease_id,
                                contractsData: contractsData,
                                blank_type: 'doc',
                            };
                            if(contractsData && contractsData.length > 0) {
                                jQuery.messager.confirm('Потвърждение', 'Страница ' + page + ' ще бъде разпечатана. Сигурни ли сте, че искате да продължите?', function (r) {
                                    if (r) {
                                        TF.Rpc.Subleases.SubleasesExports.exportContractBlank(obj)
                                            .done(function (data) {
                                                createSubleasesDownloadVariables();
                                                winDownloadContract.window('open');
                                                var path = data.file_path;
                                                _pathFile = path;
                                                _fileName = data.file_name;
                                                downloadFileContract.attr("href", path);
                                            })
                                            .fail(function (errorObj) {});
                                    }
                                });
                            } else {
                                TF.Rpc.Subleases.SubleasesExports.exportContractBlank(obj)
                                    .done(function (data) {
                                        createSubleasesDownloadVariables();
                                        winDownloadContract.window('open');
                                        var path = data.file_path;
                                        _pathFile = path;
                                        _fileName = data.file_name;
                                        downloadFileContract.attr("href", path);
                                    })
                                    .fail(function (errorObj) {});
                            }

                        } else {
                            jQuery.messager.alert('Грешка', 'Моля изберете бланка, върху която да разпечатате!');
                        }
                    }
                }, {
                    id: 'btnedittemplate',
                    text: 'Редактиране',
                    iconCls: 'icon-edit',
                    handler: function() {
                        var getChecked = jQuery('#templates-tables').datagrid('getChecked');

                        if (getChecked[0]) {
                            contracts_templates.markForEdit();

                            TF.Rpc.Common.TemplatesGrid.markForEdit(getChecked[0].id)
                            .done(function (data) {
                                jQuery('#win-add-edit-template').window('open');
                                contracts_templates.setTemplateVariables(data);
                                contracts_templates.initAddEditTemplateFields();
                            })
                            .fail(function (data) {
                                RpcErrorHandler.show(data);
                            });
                        } else {
                            jQuery.messager.alert('Грешка', 'Моля изберете бланка, която искате да редактирате!');
                        }
                    }
                }],
                onBeforeLoad: function() {
                    jQuery('#templates-tables').datagrid('clearChecked');
                },
                loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
                loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
            });
}

function addEditRentaField(params){
    var rows = jQuery('#renta-table').find('.js-renta-type-row');
    var rentaRows = rows.length;

    var removeButton = '<a id="remove-renta-row-btn-' + (rentaRows) + '" href="javaScript:void(0)" class="easyui-linkbutton" onClick="removeRentaField(' + (rentaRows) + ');"data-options="iconCls:\'icon-cancel\'">&nbsp;</a>';
    var addButton = '<a id="add-new-renta-btn" href="javaScript:void(0)" class="easyui-linkbutton" onClick="addEditRentaField();" data-options="iconCls:\'icon-add\'"></a>';

    var comboHTML ='<tr><td style="padding-left:10px"><select class="js-renta-type-row" style="width: 60px;" id="renta-type-cb-'+(rentaRows)+'"></select></td>';

    //first row
    if(rentaRows == 0) {
        comboHTML += '<td><input class="js-renta-value" style="width: 60px;" id="renta-value-'+(rentaRows)+'"></td><td>'+addButton+'</td></tr>';
    } else {
        comboHTML += '<td><input class="js-renta-value" style="width: 60px;" id="renta-value-'+(rentaRows)+'"></td><td>'+removeButton+'</td></tr>';
    }

    jQuery('#renta-table').append(comboHTML);
    jQuery('#remove-renta-row-btn-' + (rentaRows)).linkbutton({
        iconCls: 'icon-cancel',
        width:26
    });

    if(rentaRows == 0) {
        jQuery('#add-new-renta-btn').linkbutton({
            iconCls: 'icon-add',
            width:26
        });
    }

    jQuery('#renta-type-cb-'+(rentaRows)).combobox({
        url: 'index.php?common-rpc=renta-types-combobox',
        rpcParams: [{
            as_list: true
        }],
        valueField: 'id',
        textField: 'name',
        width: 205,
        onLoadSuccess: function() {
            var renta_id = 0;
            var renta_value = 0;

            if(params) {
                var renta_value = params['data']['renta_value'];
                var renta_id = params['data']['renta_id'];
            }

            jQuery('#renta-value-'+(rentaRows)).numberspinner({
                min: 0,
                precision: 3,
                missingMessage: 'Моля задайте количество на горепосочената рента в натура(количество за декар).',
                width: 95,
                value: renta_value
            });
            jQuery('#renta-type-cb-'+(rentaRows)).combobox('select', renta_id);
        },
        onSelect: function(rec){
            var numberspinnerEl = jQuery('#renta-value-'+(rentaRows));
            var rentaNatValue = 0;

            if (numberspinnerEl.data('numberspinner')) {
                rentaNatValue = numberspinnerEl.numberspinner('getValue');
            }
            var prec = 3;
            if(rec['unit'] === 3){
                prec = 0;
            }
            jQuery('#renta-value-'+(rentaRows)).numberspinner({
                min: 0,
                precision: prec,
                missingMessage: 'Моля задайте количество на горепосочената рента в натура(количество за декар).',
                value: rentaNatValue,
                width: 95,
            });
            if(jQuery('#renta-type-cb-'+(rentaRows)).combobox('getValue') == 0)
            {
                jQuery('#renta-value-'+(rentaRows)).numberspinner('setValue', '0');
            }

            var rowVars = [];
            var tmpVars = jQuery('.js-renta-type-row').length;
            for (var i = 0; i < tmpVars; i++) {
                var natura_el = jQuery('#renta-type-cb-' + i);
                var natura_el_id = jQuery('#renta-type-cb-' + i)[0].id;
                var natura_id = natura_el.combobox('getValue');
                var natura_select_el_id = jQuery(this)[0].id;

                if(jQuery.isNumeric(natura_id) && natura_el_id != natura_select_el_id) {
                    rowVars.push(natura_id);
                }
            }

            if(rec.id != 0)
            {
                var inRes = jQuery.inArray(String(rec.id),rowVars);
                if (inRes != -1 && jQuery.inArray(String(rec.id),rowVars,inRes) != -1) {
                    jQuery('#renta-error-win').window('open');
                    jQuery('#renta-type-cb-'+(rentaRows)).combobox('clear');
                    jQuery('#renta-value-'+(rentaRows)).numberspinner('setValue', '0');
                }
            }
        },
        loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
        loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
    });
}

function removeRentaField(index){
    var parent = jQuery('#renta-type-cb-'+index).parent().parent();
    parent.detach();
}

function resetEditSubleaseFields() {
    let new_farming_id = jQuery('#sublease-farming > input').combobox('getValue')
                         ? jQuery('#sublease-farming > input').combobox('getValue')
                         : null;
  
    jQuery('#sublease-type > input').combobox('select',1);
    jQuery('#sublease-number > input').val('');
    jQuery('#sublease-date > input').datebox('reset');
    jQuery('#sublease-start-date > input').datebox('reset');
    jQuery('#sublease-due-date > input').datebox('reset');
    if (new_farming_id) {
        jQuery('#sublease-farming > input').combobox('select',new_farming_id);
    }
   
    jQuery('#sv-num > input').val('');
    jQuery('#sv-date > input').datebox('reset');
    jQuery('#sublease-comment > textarea').val('');
    jQuery('#renta > input').numberspinner('clear');
    jQuery('#pd-day > input').numberspinner('clear');
    jQuery('#pd-month > input').combobox('reset');
    jQuery('#sublease-agg-type > input').combobox('reset');
    jQuery('#contract-is-subleased').prop('checked', false);

    removeRentaFields();
}

function removeRentaFields() {
    total = jQuery('.js-renta-type-row').length;
    for (var i = 0 ; i < total; i++) {
        removeRentaField(i);
    };
}

function setEditSubleaseFieldsData(data) {
    var contract_date = data.is_copy ? new Date() : data.c_date;

    jQuery('#sublease-number > input').val(data['c_num']);
    jQuery('#sublease-type > input').combobox('setValue',data['nm_usage_rights']);
    jQuery('#sv-num > input').val(data['sv_num']);
    jQuery('#sv-date > input').datebox('setValue',data['sv_date']);


    jQuery('#sublease-start-date > input').datebox({'editable': false});
    jQuery('#sublease-due-date > input').datebox({'editable': false});
    jQuery('#sublease-start-date > input').datebox({'disabled': true});
    jQuery('#sublease-due-date > input').datebox({'disabled': true});

    jQuery('#sublease-start-date > input').datebox('setValue',data['start_date']);
    jQuery('#sublease-due-date > input').datebox('setValue',data['due_date']);

    jQuery('#sublease-date > input').datebox('setValue', contract_date);
    jQuery('#renta > input').numberspinner('setValue', data['renta']);
    jQuery('#pd-day > input').numberspinner('setValue', data['pd_day']);
    jQuery('#pd-month > input').combobox('setValue', data['pd_month']);
    jQuery('#sublease-comment > textarea').val(data['comment']);
    jQuery('#contract-is-subleased').prop('checked', data.is_declaration_subleased);

    if (data.isEdit) {
        jQuery('#sublease-number > input').val(data.c_num);
    } else {
        jQuery('#sublease-number > input').val('');
    }

    if(data.isEdit || data.is_copy) {
        jQuery('#sublease-farming > input').combobox('setValue',data['farming_id']);
        jQuery('#sublease-farming > input').combobox('disable');
        if(data.additionalRentas) {
            if (data.additionalRentas.length > 0) {
                for(var i = 0; i < data.additionalRentas.length; i++){
                    var addFieldParams = {index:i, data:data.additionalRentas[i]};
                    addEditRentaField(addFieldParams);
                }
            } else {
                addEditRentaField();
            }
        } else {
            addEditRentaField();
        }
    }
    else {
        jQuery('#sublease-farming > input').combobox('enable');
    }
}

function createSubleasesDownloadVariables() {
    winDownloadContract = jQuery('#win-download').window({
        onClose: onDownloadSubleasesWindowClose
    });

    downloadFileContract = jQuery('#btn-download-file');
    cancelDownloadFileContract = jQuery('#btn-download-file-close');
}

function onDownloadSubleasesWindowClose() {
   return;
}

function initComboboxFields() {
    var farmingYearComboboxData    = JSON.parse(JSON.stringify(ComboboxData.FarmingYearCombobox)).map(year => {year.selected = false; return year}),
        contractStatusComboboxData = ComboboxData.ContractStatusCombobox,
        ekateComboboxData          = ComboboxData.EkateCombobox,
        categoryComboboxData       = ComboboxData.PlotCategoryCombobox,
        plotNTPComboboxData        = ComboboxData.PlotNTPCombobox,
        irrigatedAreaComboboxData  = ComboboxData.IrrigatedAreaCombobox,
        farmingComboboxData         = ComboboxData.FarmingCombobox,
        userFarmingPermissions      = ComboboxData.UserFarmingPermissions;

    farmingYearComboboxData[0].selected = true;
    jQuery('#search-farming-year').combobox({
        data: farmingYearComboboxData,
        valueField: 'id',
        textField: 'farming_year',
        loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
        loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
    });

    jQuery('#search-sublease-status').combobox({
        data: contractStatusComboboxData,
        editable: false,
        valueField: 'id',
        textField: 'name',
        loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
        loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
    });

    jQuery('#search-ekatte > input').combobox({
        data: ekateComboboxData,
        valueField: 'ekate',
        textField: 'text',
        filter: function(q, row){
            var opts = jQuery(this).combobox('options');
            var text = row[opts.textField].toLowerCase();
            var value = row[opts.valueField];
            var find = q.toLowerCase();
            if(text.indexOf(find) != -1 || value.indexOf(find) != -1)
            {
                return true;
            }
        },
        loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
        loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
    });

    jQuery('#search-ekatte').combobox({
        data: ekateComboboxData,
        valueField: 'ekate',
        textField: 'text',
        multiple: true,
        onHidePanel: onHidePanelMultiSelect,
        onSelect: onComboMultiSelect,
        onLoadSuccess: function() {
            jQuery('#search-plot-ekate > input').combobox({
                data: jQuery('#search-ekatte').combobox('getData'),
                valueField: 'ekate',
                textField: 'text',
                filter: function(q, row){
                    var opts = jQuery(this).combobox('options');
                    var text = row[opts.textField].toLowerCase();
                    var value = row[opts.valueField];
                    var find = q.toLowerCase();
                    if(text.indexOf(find) != -1 || value.indexOf(find) != -1)
                    {
                        return true;
                    }
                }
            });
        },
        filter: function(q, row){
            var opts = jQuery(this).combobox('options');
            var text = row[opts.textField].toLowerCase();
            var value = row[opts.valueField];
            var find = q.toLowerCase();
            if(text.indexOf(find) != -1 || value.indexOf(find) != -1)
            {
                return true;
            }
        },
        loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
        loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
    });
    jQuery('#search-category').combobox({
        data: categoryComboboxData,
        valueField: 'id',
        textField: 'name',
        multiple: true,
        onHidePanel: onHidePanelMultiSelect,
        onSelect: onComboMultiSelect,
        loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
        loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
    });
    jQuery('#search-area-type').combobox({
        data: plotNTPComboboxData,
        editable: false,
        valueField: 'id',
        textField: 'name',
        multiple: true,
        onHidePanel: onHidePanelMultiSelect,
        onSelect: onComboMultiSelect,
        loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
        loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
    });
    jQuery('#search-renta-types').combobox({
        url: 'index.php?common-rpc=renta-types-combobox',
        valueField: 'id',
        textField: 'name',
        multiple: true,
        onHidePanel: onHidePanelMultiSelect,
        onSelect: onComboMultiSelect,
        loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
        loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
    });

    irrigatedAreaComboboxData[0].selected = true;
    jQuery('#search-irrigated-area').combobox({
        data: irrigatedAreaComboboxData,
        editable: false,
        valueField: 'value',
        textField: 'label',
        multiple: false,
        loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
        loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
    });

    farmingComboboxData.forEach(function (el) {
        if (el.id !== "") {
            el.selected = false;
            let hasPermission = Object.values(userFarmingPermissions).includes(el.id);
            if(!hasPermission) {
                //disable all farmings with no permissions
                el.disabled = true;
            } else {
                //auto filter farmings with permissions
                el.selected = true;
            }
        }
    });

    jQuery('#search-farming  > input').combobox({
        data: farmingComboboxData,
        editable: false,
        valueField: 'id',
        textField: 'name',
        multiple: true,
        onHidePanel: onHidePanelMultiSelect,
        loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
        loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
    });

    jQuery('#search-type > input').combobox({
        data: ComboboxData.SubleasedContractType,
        editable: false,
        valueField: 'id',
        textField: 'name',
        multiple: true,
        onHidePanel: onHidePanelMultiSelect ,
        loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
        loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
    });
}

function initSearchOnEnter() {
    jQuery("#win-filter-subleases").off("keyup").on("keyup", function (event) {
		// Listen for enter key
        if (13 !== event.keyCode) {
			return;
		}
		jQuery("#btn-filter-subleases-tree").click()
    });
}
