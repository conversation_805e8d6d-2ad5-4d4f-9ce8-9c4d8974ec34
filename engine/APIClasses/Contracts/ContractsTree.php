<?php

namespace TF\Engine\APIClasses\Contracts;

use DateTime;
use Exception;
use PHPExcel_Style_NumberFormat;
use Prado\Prado;
use Prado\Web\Services\TRpcApiProvider;
use TF\Application\Common\Config;
use TF\Application\Entity\ObjectPermissions;
use TF\Application\Entity\UserFarmings;
use TF\Engine\APIClasses\KVSContractsUpdate\KVSContractsUpdate;
use TF\Engine\Kernel\ArrayHelper;
use TF\Engine\Kernel\ExportToExcelClass;
use TF\Engine\Kernel\MTRpcException;
use TF\Engine\Plugins\Core\Contracts\SalesContractValidationTrait;
use TF\Engine\Plugins\Core\Farming\FarmingController;
use TF\Engine\Plugins\Core\UserDb\UserDbController;
use TF\Engine\Plugins\Core\UserDbAreaTypes\UserDbAreaTypesController;
use TF\Engine\Plugins\Core\UserDbContracts\UserDbContractsController;
use TF\Engine\Plugins\Core\UserDbOwners\UserDbOwnersController;
use TF\Engine\Plugins\Core\UserDbPayments\UserDbPaymentsController;
use TF\Engine\Plugins\Core\Users\UsersController;

/**
 * Дърво "Договори".
 *
 * @rpc-module Contracts
 *
 * @rpc-service-id contracts-tree
 */
class ContractsTree extends TRpcApiProvider
{
    use KVSContractsUpdate;
    use SalesContractValidationTrait;
    public const CONTRACTS_AVAILABLE_PLOT_AREA_EXCEPTION = -34059;

    private $module = 'Contracts';
    private $service_id = 'contracts-tree';

    /**
     * Register the available methods.
     *
     * @return array the list of implemented methods
     */
    public function registerMethods()
    {
        return [
            'read' => ['method' => [$this, 'getContractsTree'],
                'validators' => [
                    'filterObj' => 'validateArray',
                    'page' => 'validateInteger',
                    'rows' => 'validateInteger',
                    'sort' => 'validateSort',
                    'order' => 'validateOrder',
                ], ],
            'add' => ['method' => [$this, 'addEditContract'],
                'validators' => [
                    'record_id' => 'validateInteger',
                    'contract_type' => 'validateInteger, validateRequired',
                    'contract_number' => 'validateText, validateRequired',
                    'contract_date' => 'validateDate, validateRequired',
                    'contract_start_date' => 'validateDate',
                    'contract_due_date' => 'validateDate',
                    'sv_num' => 'validateText',
                    'sv_date' => 'validateDate',
                    'contract_farming' => 'validateInteger',
                    'na_num' => 'validateText',
                    'tom' => 'validateText',
                    'delo' => 'validateText',
                    'court' => 'validateText',
                    'renta' => 'validateNumber',
                    'renta_natura_type' => 'validateInteger',
                    'renta_natura' => 'validateNumber',
                    'contract_agg_type' => 'validateInteger',
                    'additionalRentas' => [
                        'type' => 'validateInteger',
                        'value' => 'validateNumber',
                    ],
                ]],
            'checkForExistance' => ['method' => [$this, 'checkForExistance']],
            'changeActiveStatus' => ['method' => [$this, 'changeActiveStatus']],
            'hasContractEditedPlots' => ['method' => [$this, 'hasContractEditedPlots']],
            'contractCopy' => ['method' => [$this, 'initContractCopy']],
            'markForEdit' => ['method' => [$this, 'markForEdit']],
            'addAnnex' => ['method' => [$this, 'addAnnex'],
                'validators' => [
                    'annex_parent_id' => 'validateInteger, validateRequired',
                    'annex_start_date' => 'validateDate, validateRequired',
                    'annex_due_date' => 'validateDate',
                    'annex_num' => 'validateText',
                    'annex_date' => 'validateDate',
                    'annex_sv_num' => 'validateText',
                    'farming_id' => 'validateInteger',
                    'annex_comment' => 'validateText',
                    'nm_usage_rights' => 'validateInteger',
                    'annex_sv_date' => 'validateDate',
                    'annex_pd_day' => 'validateInteger',
                    'annex_pd_month' => 'validateInteger',
                    'annex_renta_nat_type' => 'validateInteger',
                    'annex_renta_nat' => 'validateNumber',
                    'annex_renta' => 'validateNumber',
                    'additionalRentas' => [
                        'annex_renta_nat_type' => 'validateInteger',
                        'annex_renta_nat' => 'validateNumber',
                    ],
                    'added_plots_data' => [
                        'document_area' => 'validateNumber',
                        'contract_area' => 'validateNumber',
                        'gid' => 'validateInteger',
                    ],
                    'removed_plots_gids' => 'validateIntegerArray',
                ]],
            'deleteContracts' => ['method' => [$this, 'deleteContracts']],
            'initAreaEqualize' => ['method' => [$this, 'initAreaEqualize']],
            'getAllFilteredContracts' => ['method' => [$this, 'getAllFilteredContracts']],
            'saveMultiEditContracts' => ['method' => [$this, 'saveMultiEditContracts']],
            'multiCopyContracts' => ['method' => [$this, 'multiCopyContracts'],
                'validators' => [
                    'contract_ids' => 'validateIntegerArray, validateRequired',
                ],
            ],
            'checkForLongerThanYear' => ['method' => [$this, 'checkForLongerThanYear']],
            'initAnnexRenta' => ['method' => [$this, 'initAnnexRenta']],
            'exportFilteredContractsToXLS' => ['method' => [$this, 'exportFilteredContractsToXLS']],
        ];
    }

    /**
     * Gets all contracts, associated with the current user.
     *
     * @api-method read
     *
     * @param array $filterObj contains all filter parameters
     *                         {
     *                         #item string c_num
     *                         #item string na_num
     *                         #item array c_type
     *                         {
     *                         #item integer contract_types
     *                         }
     *                         #item integer c_status
     *                         #item array farming
     *                         {
     *                         #item integer farming_ids
     *                         }
     *                         #item integer farming_year
     *                         #item array renta_types
     *                         {
     *                         #item integer renta_types
     *                         }
     *                         #item timestamp date_from
     *                         #item timestamp date_to
     *                         #item timestamp due_date_from
     *                         #item timestamp due_date_to
     *                         #item bool   with_renta_nat
     *                         #item string kad_ident
     *                         #item array ekate
     *                         {
     *                         #item string EKATTE identifiers
     *                         }
     *                         #item string masiv
     *                         #item string number
     *                         #item array category
     *                         {
     *                         #item integer category ids
     *                         }
     *                         #item array ntp
     *                         {
     *                         #item integer NPT ids
     *                         }
     *                         #item string owner_name
     *                         #item string owner_egn
     *                         #item string owner_type
     *                         #item string rep_name
     *                         #item string rep_egn
     *                         #item string company_name
     *                         #item string company_eik
     *                         #item string block
     *                         }
     * @param int $page pagination parameters
     * @param int $rows pagination parameters
     * @param string $sort pagination parameters
     * @param string $order pagination parameters
     *
     * @return array array of relevant to user, sorted and filtered contracts
     */
    public function getContractsTree(array $filterObj, int $page = null, int $rows = null, ?string $sort = '', ?string $order = '')
    {
        if ($this->User->isGuest || !$this->User->Database) {
            return [];
        }

        $arrayHelper = new ArrayHelper();
        // init controllers
        $UserDbController = new UserDbController($this->User->Database);
        $FarmingController = new FarmingController('Farming');
        $farmings = $FarmingController->getUserFarmings(true);

        $userFarmingIds = array_keys($farmings);
        $farmingIds = $arrayHelper->filterEmptyStringArr($filterObj['farming']);
        $farmingIds = isset($farmingIds) && count($farmingIds) > 0
            ? array_intersect($farmingIds, $userFarmingIds)
            : $userFarmingIds;
        $filterObj['farming'] = $farmingIds;

        // get renta natura types and create predefined array
        $renta_types = [];
        // options for renta nat types
        $options = [
            'tablename' => $UserDbController->DbHandler->tableRentaTypes,
        ];
        $renta_results = $UserDbController->getItemsByParams($options, false, false);
        $renta_resultsCount = count($renta_results);
        for ($i = 0; $i < $renta_resultsCount; $i++) {
            $renta_types[$renta_results[$i]['id']]['name'] = $renta_results[$i]['name'];
            $renta_types[$renta_results[$i]['id']]['unit'] = $GLOBALS['Contracts']['renta_units'][$renta_results[$i]['unit']]['name'];
        }

        if (null == $filterObj) {
            $filterObj = [];
        }

        $results = $this->executeContractsQuery(false, false, true, false, $filterObj, $page, $rows, $sort, $order);

        $resultsCount = count($results);

        if (0 == $resultsCount) {
            return [];
        }

        $formattedResult = $this->formatTree($results, $farmings, $renta_types);

        // add attribute to first listed element of three for custom pagination
        // information for total can not be sent otherwise
        $key = array_key_first($formattedResult['rows']);
        $formattedResult['rows'][$key]['attributes']['pagination']['total'] = $formattedResult['full_count'];
        $formattedResult['rows'][$key]['attributes']['pagination']['limit'] = $rows;

        return $formattedResult['rows'];
    }

    public function executeContractsQuery(bool $counter, bool $returnOnlySQL, bool $includeAnnexesInSearch, bool $is_annex, $filterObj = null, int $page = null, int $rows = null, ?string $sort = '', ?string $order = '')
    {
        $FarmingController = new FarmingController('Farming');
        $UserDbContractsController = new UserDbContractsController($this->User->Database);
        $UserDbAreaTypesController = new UserDbAreaTypesController($this->User->Database);

        date_default_timezone_set('Europe/Sofia');
        $currentDate = date('Y-m-d', time());
        $arrayHelper = $FarmingController->ArrayHelper;
        $ekate = $arrayHelper->filterEmptyStringArr($filterObj['ekate']);
        if ($returnOnlySQL) {
            $category = $arrayHelper->mapArrayElToStr($arrayHelper->filterEmptyStringArr($filterObj['category']));
        } else {
            $category = $arrayHelper->filterEmptyStringArr($filterObj['category']);
        }

        if (null != $filterObj) {
            $filterObj['owner_name'] = $filterObj['owner_name'] ? preg_replace('/\s+/', ' ', $filterObj['owner_name']) : '';
            $filterObj['rep_name'] = $filterObj['rep_name'] ? preg_replace('/\s+/', ' ', $filterObj['rep_name']) : '';
            $filterObj['company_name'] = $filterObj['company_name'] ? preg_replace('/\s+/', ' ', $filterObj['company_name']) : '';
        }

        if (isset($filterObj['from_alert'], $_SESSION['alert_filters']) && 1 == $filterObj['from_alert'] && array_key_exists('expiring_contracts', $_SESSION['alert_filters']) && !empty($_SESSION['alert_filters']['expiring_contracts'])) {
            $filterObj['contract_id'] = implode(',', $_SESSION['alert_filters']['expiring_contracts']);
        }

        $ntpFilter = [];
        if (!empty($filterObj['ntp'])) {
            $ntpFilter = $UserDbAreaTypesController->getNtpCodesWithAdditionalCodes($filterObj['ntp']);
        }

        $groupBy = 'c.id, cg.name';

        if (true == $is_annex) {
            $groupBy = 'c.id,cg.name,a.id';
        }

        // prepare options for contract query
        $options = [
            'where' => [
                'is_sublease' => ['column' => 'is_sublease', 'compare' => '=', 'prefix' => 'c', 'value' => 'FALSE'],
                // filters
                // contract filter
                'contract_id' => ['column' => 'id', 'compare' => 'IN', 'prefix' => 'c', 'value' => $arrayHelper->filterEmptyStringArr(explode(',', $filterObj['contract_id']))],
                'c_num' => ['column' => 'c_num', 'compare' => 'ILIKE', 'prefix' => 'c', 'value' => trim($filterObj['c_num'])],
                'na_num' => ['column' => 'na_num', 'compare' => 'ILIKE', 'prefix' => 'c', 'value' => trim($filterObj['na_num'])], // Нотар. акт
                'c_type' => ['column' => 'nm_usage_rights', 'compare' => 'IN', 'prefix' => 'c', 'value' => $arrayHelper->filterEmptyStringArr($filterObj['c_type'])],
                'farming' => ['column' => 'farming_id', 'compare' => 'IN', 'prefix' => 'c', 'value' => $arrayHelper->filterEmptyStringArr($filterObj['farming'])],
                'renta_types' => ['column' => 'renta_id', 'compare' => 'IN', 'prefix' => 'cr', 'value' => $arrayHelper->filterEmptyStringArr($filterObj['renta_types'])],
                // contract dates
                'start_date_from' => ['column' => 'start_date', 'compare' => '>=', 'prefix' => 'c', 'value' => $filterObj['date_from']],
                'due_date_to' => ['column' => 'due_date', 'compare' => '<=', 'prefix' => 'c', 'value' => $filterObj['date_to']],
                // plot filter
                'kad_ident' => ['column' => 'kad_ident', 'compare' => '=', 'prefix' => 'kvs', 'value' => $filterObj['kad_ident']],
                'ekate' => ['column' => 'ekate', 'compare' => 'IN', 'prefix' => 'kvs', 'value' => $ekate],
                'masiv' => ['column' => 'masiv', 'compare' => '=', 'prefix' => 'kvs', 'value' => $filterObj['masiv']],
                'number' => ['column' => 'number', 'compare' => '=', 'prefix' => 'kvs', 'value' => $filterObj['number']],
                'category' => ['column' => 'category', 'compare' => 'IN', 'prefix' => 'kvs', 'value' => $category],
                'area_type' => ['column' => 'area_type', 'compare' => 'IN', 'prefix' => 'kvs', 'value' => $ntpFilter],
                'block' => ['column' => 'block', 'compare' => '=', 'prefix' => 'kvs', 'value' => $filterObj['block']],
                // owner filter
                'owner_type' => ['column' => 'owner_type', 'compare' => '=', 'prefix' => 'o', 'value' => $filterObj['owner_type']],
                'rep_name' => ['column' => "TRIM(o_r.rep_name) || ' ' || TRIM(o_r.rep_surname) || ' ' || TRIM(o_r.rep_lastname)", 'compare' => 'ILIKE', 'value' => $filterObj['rep_name']],
                'rep_egn' => ['column' => 'rep_egn', 'compare' => '=', 'prefix' => 'o_r', 'value' => $filterObj['rep_egn']],
                'company_name' => ['column' => 'TRIM(o.company_name)', 'compare' => 'ILIKE', 'value' => $filterObj['company_name']],
                'company_eik' => ['column' => 'eik', 'compare' => '=', 'prefix' => 'o', 'value' => $filterObj['company_eik']],
                'owner_note' => ['column' => 'remark', 'compare' => 'ILIKE', 'prefix' => 'o', 'value' => $filterObj['owner_note']],
            ],
            'group' => $groupBy,
        ];

        $options['return'] = [
            'c.*',
            '(CASE WHEN COUNT(p.amount) is null then 0 ELSE COUNT(p.amount) END)as payment',
            'STRING_AGG(DISTINCT(cr.renta_id)::TEXT, \',\') as renta_ids',
            '(CASE WHEN SUM (pc.rent_per_plot) IS NULL THEN FALSE ELSE true END) as has_rent_per_plot',
            'cg.name as group_name',
        ];

        if (!empty($filterObj['c_group'])) {
            $options['where']['c_group'] = ['column' => 'group', 'compare' => 'IN', 'prefix' => 'c', 'value' => $filterObj['c_group']];
        }

        if (false == $includeAnnexesInSearch) {
            $options['where']['is_annex'] = ['column' => 'is_annex', 'compare' => '=', 'prefix' => 'c', 'value' => ($is_annex) ? 'TRUE' : 'FALSE'];
        } else {
            $options['union_annex_parents'] = true;
        }

        if (!empty(trim($filterObj['ao_c_num']))) {
            $options['where']['ao_c_num'] = ['column' => 'ao_c_num', 'compare' => '=', 'prefix' => 'c', 'value' => trim($filterObj['ao_c_num'])];
        }

        if ($filterObj['without_from_subleases']) {
            $options['where']['from_sublease'] = ['column' => 'from_sublease', 'compare' => 'IS', 'prefix' => 'c', 'value' => 'null'];
        }
        if ($filterObj['farming_year']) {
            $options['whereCustom']
                = "AND c.start_date <= '"
                . $GLOBALS['Farming']['years'][$filterObj['farming_year']]['year'] . "-09-30' AND (c.due_date >= '"
                . ($GLOBALS['Farming']['years'][$filterObj['farming_year']]['year'] - 1) . "-10-01' OR c.due_date IS NULL)";
        }
        if ($filterObj['notification_list']) {
            $options['where']['c_id'] = [
                'column' => 'id', 'compare' => 'IN', 'prefix' => 'c', 'value' => $filterObj['notification_list'],
            ];
        }

        // adding with renta nat filter
        if (isset($filterObj['with_renta_nat']) && true === $filterObj['with_renta_nat']) {
            $options['where']['with_renta_nat'] = [
                'column' => 'renta_id',
                'prefix' => 'cr',
                'compare' => '>',
                'value' => '0',
            ];
        }
        // adding without renta nat filter
        if (isset($filterObj['with_renta_nat']) && false === $filterObj['with_renta_nat']) {
            $options['where']['with_renta_nat'] = [
                'column' => 'renta_id',
                'prefix' => 'cr',
                'compare' => 'IS',
                'value' => 'NULL',
            ];
        }

        if ($filterObj['person_name']) {
            $tmp_person_names = preg_replace('/\s+/', '.*', $filterObj['person_name']);
            $tmp_person_names = mb_strtolower($tmp_person_names, 'UTF-8');
            $options['whereOr']['owner_names'] = ['column' => "lower(TRIM (o.name)) || ' ' || lower(TRIM (o.surname)) || ' ' || lower(TRIM (o.lastname))", 'compare' => '~', 'value' => $tmp_person_names];
            $options['whereOr']['rep_names'] = ['column' => "lower(TRIM (o_r.rep_name)) || ' ' || lower(TRIM (o_r.rep_surname)) || ' ' || lower(TRIM (o_r.rep_lastname))", 'compare' => '~', 'value' => $tmp_person_names];
        }

        if ($filterObj['person_egn']) {
            $options['whereOr']['owner_egn'] = ['column' => 'egn', 'compare' => 'ILIKE', 'prefix' => 'o', 'value' => $filterObj['person_egn']];
            $options['whereOr']['rep_egn'] = ['column' => 'rep_egn', 'compare' => 'ILIKE', 'prefix' => 'o_r', 'value' => $filterObj['person_egn']];
        }

        if (isset($filterObj['owner_phone']) && '' != $filterObj['owner_phone']) {
            $options['whereOr']['phone'] = ['column' => 'phone', 'compare' => 'ILIKE', 'prefix' => 'o', 'value' => $filterObj['owner_phone']];
            $options['whereOr']['mobile'] = ['column' => 'mobile', 'compare' => 'ILIKE', 'prefix' => 'o', 'value' => $filterObj['owner_phone']];
        }

        if ($filterObj['owner_name']) {
            $tmp_owner_names = preg_replace('/\s+/', '.*', $filterObj['owner_name']);
            $tmp_owner_names = mb_strtolower($tmp_owner_names, 'UTF-8');
            $options['where']['owner_names'] = ['column' => "lower(TRIM (o.NAME)) || ' ' || lower(TRIM (o.surname)) || ' ' || lower(TRIM (o.lastname))", 'compare' => '~', 'value' => $tmp_owner_names];
        }

        if ($filterObj['owner_egn']) {
            $options['where']['owner_egn'] = ['column' => 'egn', 'prefix' => 'o', 'compare' => 'ILIKE', 'value' => $filterObj['owner_egn']];
        }

        if ($filterObj['heritor_name']) {
            $tmp_heritor_names = preg_replace('/\s+/', '.*', $filterObj['heritor_name']);
            $tmp_heritor_names = mb_strtolower($tmp_heritor_names, 'UTF-8');
            $UserDbOwnersController = new UserDbOwnersController($this->User->Database);

            $tmpOwnerIdOption = [
                'return' => [
                    'id',
                ],
                'where' => [
                    'heritor_name' => ['column' => "lower(TRIM (NAME)) || ' ' || lower(TRIM (surname)) || ' ' || lower(TRIM (lastname))", 'compare' => '~', 'value' => $tmp_heritor_names],
                ],
                'tablename' => $UserDbOwnersController->DbHandler->tableOwners,
            ];

            $tmpHeritorsArray = $this->getHeritorsOwners($tmpOwnerIdOption);
            if (!empty($tmpHeritorsArray)) {
                $options['where']['pc_rel_id'] = ['column' => 'pc_rel_id', 'prefix' => 'po', 'compare' => 'IN', 'value' => $tmpHeritorsArray];
            } else {
                return [];
            }
        }

        if ($filterObj['heritor_egn']) {
            $UserDbOwnersController = new UserDbOwnersController($this->User->Database);

            $tmpOwnerIdOption = [
                'return' => [
                    'id',
                ],
                'where' => [
                    'heritor_egn' => ['column' => 'egn', 'compare' => 'ILIKE', 'value' => $filterObj['heritor_egn']],
                ],
                'tablename' => $UserDbOwnersController->DbHandler->tableOwners,
            ];

            $tmpHeritorsArrayEgn = $this->getHeritorsOwners($tmpOwnerIdOption);
            if (!empty($tmpHeritorsArrayEgn)) {
                $options['where']['pc_rel_id'] = ['column' => 'pc_rel_id', 'prefix' => 'po', 'compare' => 'IN', 'value' => $tmpHeritorsArrayEgn];
            } else {
                return [];
            }
        }

        if ($filterObj['rep_name']) {
            $tmp_rep_names = preg_replace('/\s+/', '.*', $filterObj['rep_name']);
            $tmp_rep_names = mb_strtolower($tmp_rep_names, 'UTF-8');
            $options['where']['rep_names'] = ['column' => "lower(TRIM (o_r.rep_NAME)) || ' ' || lower(TRIM (o_r.rep_surname)) || ' ' || lower(TRIM (o_r.rep_lastname))", 'compare' => '~', 'value' => $tmp_rep_names];
        }

        if ($filterObj['rep_egn'] || $filterObj['rep_name'] || $filterObj['heritor_egn'] || $filterObj['heritor_name'] || $filterObj['owner_name'] || $filterObj['owner_egn']) {
            $options['where']['is_heritor'] = ['column' => 'is_heritor', 'compare' => '=', 'prefix' => 'po', 'value' => 'false'];
        }

        if ($filterObj['c_status']) {
            $options['where']['active'] = ['column' => 'get_contract_status(c.id, c.active, c.start_date, c.due_date)::VARCHAR', 'compare' => '=', 'value' => $filterObj['c_status']];
        }

        if ('all' != $filterObj['irrigated_area']) {
            $options['where']['irrigated_area'] = ['column' => 'irrigated_area', 'compare' => '=', 'prefix' => 'kvs', 'value' => $filterObj['irrigated_area']];
        }

        if (true == $filterObj['c_num_complete_match']) {
            $options['where']['c_num']['compare'] = '=';
        }

        if (true == $filterObj['na_num_complete_match']) {
            $options['where']['na_num']['compare'] = '=';
        }

        if (true == $filterObj['filter_rent_per_plot']) {
            $options['where']['with_rent_per_plot'] = ['column' => '(select (CASE WHEN SUM (rent_per_plot) IS NULL THEN FALSE ELSE TRUE END) from su_contracts_plots_rel where contract_id = c.id)', 'compare' => '=', 'value' => 'true'];
        }

        if (isset($filterObj['is_closed_for_editing'])) {
            $options['where']['is_closed_for_editing'] = [
                'column' => 'is_closed_for_editing',
                'compare' => '=',
                'prefix' => 'c',
                'value' => $filterObj['is_closed_for_editing'],
            ];
        }

        if (isset($filterObj['ownerless_contracts']) && true == $filterObj['ownerless_contracts']) {
            $options['having'] = 'array_agg(po.id) FILTER (WHERE po.id is null) is not null and array_agg(f_r.id) FILTER (WHERE f_r.id is null) is not null';
        }

        if ($filterObj['contract_note']) {
            $options['orWhere']['contract_note'] = [
                'contract_note_contract' => ['column' => 'comment', 'prefix' => 'c', 'compare' => 'ILIKE', 'value' => $filterObj['contract_note']],
                'contract_note_annex' => ['column' => 'comment', 'prefix' => 'a', 'compare' => 'ILIKE', 'value' => $filterObj['contract_note']],
            ];
        }

        // pagination parameters
        if (!$is_annex && !$counter) {
            if (!empty($sort) && 'c_num' === $sort) {
                $sort = 'c.c_num';
            }
            $options['sort'] = 'id' == $sort ? 'c.id' : $sort;
            if (true == $includeAnnexesInSearch) {
                $options['sort'] = $sort;
            }

            $options['order'] = 'id' == $order ? 'c.id' : $order;
            $options['offset'] = ($page - 1) * $rows < 0 ? 0 : ($page - 1) * $rows;
            $options['limit'] = $rows;
        }

        if (false == $includeAnnexesInSearch) {
            return $UserDbContractsController->getFullContractDataByFilter($options, $counter, $returnOnlySQL);
        }

        return $UserDbContractsController->getContracts($options, $counter, $returnOnlySQL);
    }

    /**
     * Checks whether the current contract name exists.
     *
     * @api-method checkForExistance
     *
     * @param array $rpcParams
     *                         {
     *                         #item string  contract_number
     *                         #item integer farming
     *                         }
     *
     * @throws MTRpcException if contract exists
     *
     * @return bool true if the name is avaliable
     */
    public function checkForExistance($rpcParams)
    {
        $UserDbController = new UserDbController($this->User->Database);
        $options = [
            'tablename' => $UserDbController->DbHandler->tableContracts,
            'where' => [
                'c_num' => ['column' => 'c_num', 'compare' => '=', 'value' => $rpcParams['contract_number']],
                'farming_id' => ['column' => 'farming_id', 'compare' => '=', 'value' => $rpcParams['farming']],
                'is_sublease' => ['column' => 'is_sublease', 'compare' => '=', 'value' => 'false'],
                'is_annnex' => ['column' => 'is_annex', 'compare' => '=', 'value' => 'false'],
            ],
        ];

        $count = $UserDbController->getItemsByParams($options, true, false);

        if ($count[0]['count'] && !$rpcParams['record_id']) {
            throw new MTRpcException('existing_contract_number', -33203);
        }

        return true;
    }

    /**
     * Saves the new/edited contract data.
     *
     * @api-method add
     *
     * @param array $rpcParams
     *                         {
     *                         #item integer record_id
     *                         #item integer contract_type
     *                         #item string  contract_number
     *                         #item date    contract_date
     *                         #item string  sv_num
     *                         #item date    sv_date
     *                         #item date    contract_start_date
     *                         #item date    contract_due_date
     *                         #item integer contract_farming
     *                         #item string  na_num
     *                         #item string  tom
     *                         #item string  delo
     *                         #item string  court
     *                         #item string  comment
     *                         #item float   renta
     *                         #item integer renta_natura_type
     *                         #item float   renta_natura
     *                         #item integer contract_agg_type
     *                         #item array   additionalRentas
     *                         #item boolean is_declaration_subleased
     *                         {
     *                         #item integer type
     *                         #item float value
     *                         }
     *                         }
     *
     * @throws MTRpcException if contract exists
     *
     * @return int
     */
    public function addEditContract($rpcParams)
    {
        $UserDbController = new UserDbController($this->User->Database);
        $UsersController = new UsersController('Users');
        $UserDbContractsController = new UserDbContractsController($this->User->Database);
        $UserDbPaymentsController = new UserDbPaymentsController($this->User->Database);
        if (Config::CONTRACT_TYPE_OWN == $rpcParams['contract_type'] && !$this->User->HasContractsOwnWriteRights) {
            throw new MTRpcException('invalid_user_contracts_rights_own_contracts', -33200);
        }

        $paymentStatus = false;

        try {
            // Disable it in order to make faster adding plots into contracts. It is enable again below
            $UserDbController->disableRentaMatViewTriggers();

            if (0 != $rpcParams['record_id']) {
                // editing contract
                $isFromSublease = $UserDbContractsController->isContractFromSublease($rpcParams['record_id']);
                if ($isFromSublease) {
                    throw new MTRpcException('CANNOT_MODIFY_FROM_SUBLEASE_CONTRACT', -33752);
                }

                $oldOptions = [
                    'tablename' => $UserDbController->DbHandler->tableContracts . ' c',
                    'return' => ['c.*', "string_agg(scr.renta_id::text, ',') as renta_nat_ids", "string_agg(scr.renta_value::text, ',') as renta_nat_values"],
                    'where' => [
                        'id' => ['column' => 'id', 'compare' => '=', 'prefix' => 'c', 'value' => $rpcParams['record_id']],
                    ],
                    'joins' => [
                        'left join su_contracts_rents scr on scr.contract_id = c.id',
                    ],
                    'group' => 'c.id',
                ];

                $oldContractData = $UserDbController->getItemsByParams($oldOptions, false, false);

                $startDateIsChanged = date('Y-m-d', strtotime($oldContractData[0]['start_date'])) != date('Y-m-d', strtotime($rpcParams['contract_start_date']));

                if (Config::CONTRACT_TYPE_OWN == $rpcParams['contract_type']
                    && (
                        $oldContractData[0]['farming_id'] != $rpcParams['contract_farming']
                        || $startDateIsChanged
                    )
                ) {
                    $params = [
                        'contract_id' => $rpcParams['record_id'],
                        'farming_id' => $oldContractData[0]['farming_id'],
                    ];

                    if ($startDateIsChanged) {
                        $params['start_date'] = $rpcParams['contract_start_date'];
                    }

                    $this->validateSalesContractRelations(
                        $params,
                        $UserDbContractsController
                    );
                }

                $this->validateAnnexDates($rpcParams, $oldContractData[0]);

                if ($oldContractData[0]['nm_usage_rights'] != $rpcParams['contract_type']
                    && !(
                        in_array($rpcParams['contract_type'], [Config::CONTRACT_TYPE_LEASE, Config::CONTRACT_TYPE_RENT, Config::CONTRACT_TYPE_JOINT_PROCESSING])
                        && in_array($oldContractData[0]['nm_usage_rights'], [Config::CONTRACT_TYPE_LEASE, Config::CONTRACT_TYPE_RENT, Config::CONTRACT_TYPE_JOINT_PROCESSING])
                    )
                ) {
                    throw new MTRpcException('SYSTEM_ERROR', -32603);
                }

                $oldContractData[0]['renta_nat_ids'] = explode(',', $oldContractData[0]['renta_nat_ids']);
                $oldContractData[0]['renta_nat_values'] = explode(',', $oldContractData[0]['renta_nat_values']);

                $hasRentaNatChanges = false;

                if (1 == count($oldContractData[0]['renta_nat_ids']) && empty($oldContractData[0]['renta_nat_ids'][0])) {
                    $newRentsInKind = array_filter($rpcParams['additionalRentas'], function ($item) {
                        return $item['value'] > 0;
                    });
                    if (count($newRentsInKind) > 0) {
                        $hasRentaNatChanges = true;
                    }
                } elseif (count($oldContractData[0]['renta_nat_ids']) != count($rpcParams['additionalRentas'])) {
                    if (count($oldContractData[0]['renta_nat_ids']) < count($rpcParams['additionalRentas'])) {
                        $newRentsInKind = array_filter($rpcParams['additionalRentas'], function ($item) use ($oldContractData) {
                            return !in_array($item['type'], $oldContractData[0]['renta_nat_ids']) && $item['value'] > 0;
                        });

                        if (count($newRentsInKind) > 0) {
                            $hasRentaNatChanges = true;
                        }
                    } else {
                        $hasRentaNatChanges = true;
                    }
                } else {
                    foreach ($oldContractData[0]['renta_nat_ids'] as $key => $rentaNatId) {
                        if (
                            $rentaNatId != $rpcParams['additionalRentas'][$key]['type']
                            || $oldContractData[0]['renta_nat_values'][$key] != $rpcParams['additionalRentas'][$key]['value']) {
                            $hasRentaNatChanges = true;

                            break;
                        }
                    }
                }

                // Restrictions when editing a contract/annex if a payment is made. Restriction only if the following fields are changed: start_date, due_date, farming_id, renta and renta_natura [GPS-3842]
                if (
                    date('Y-m-d', strtotime($oldContractData[0]['start_date'])) != date('Y-m-d', strtotime($rpcParams['contract_start_date']))
                    || date('Y-m-d', strtotime($oldContractData[0]['due_date'])) != date('Y-m-d', strtotime($rpcParams['contract_due_date']))
                    || $oldContractData[0]['farming_id'] != $rpcParams['contract_farming']
                    || round((float)$oldContractData[0]['renta'], 2) != round((float)$rpcParams['renta'], 2)
                    || $oldContractData[0]['overall_renta'] != $rpcParams['overall_renta']
                    || $hasRentaNatChanges
                ) {
                    $UserDbPaymentsController->hasPaymentRestriction($rpcParams['record_id'], [
                        'start_date' => $rpcParams['contract_start_date'],
                        'due_date' => $rpcParams['contract_due_date'],
                        'parent_id' => $oldContractData[0]['parent_id'],
                    ]);

                    // Check for payments in the period before changing the contract dates
                    $UserDbPaymentsController->hasPaymentRestriction($rpcParams['record_id'], [
                        'start_date' => $oldContractData[0]['start_date'],
                        'due_date' => $oldContractData[0]['due_date'],
                        'parent_id' => $oldContractData[0]['parent_id'],
                    ]);
                }

                $excludeContract = null;
                if (!empty($oldContractData[0]['parent_id'])) {
                    $excludeContract = [$oldContractData[0]['parent_id']];
                }

                // Get contract plots
                $options = [
                    'tablename' => $UserDbController->DbHandler->contractsPlotsRelTable,
                    'where' => [
                        'contract_id' => ['column' => 'contract_id', 'compare' => '=', 'value' => $rpcParams['record_id']],
                        'annex_action' => ['column' => 'annex_action', 'compare' => '=', 'value' => 'added'],
                    ],
                ];
                $plots = $UserDbController->getItemsByParams($options);

                // Check contracts plots exist in another contract with overlapping period
                $plotsWithActiveContracts = $UserDbContractsController->getNotAvailableContractsPlots(
                    $rpcParams['contract_start_date'],
                    $rpcParams['contract_due_date'],
                    [$rpcParams['record_id']],
                    ['editContract' => true],
                    array_column($plots, 'plot_id'),
                    $excludeContract,
                    null
                );

                $UserDbContractsController->validatePlotAreas($plots, $plotsWithActiveContracts);

                if ($rpcParams['contract_farming'] != $oldContractData[0]['farming_id']) {
                    $UserDbContractsController->isPlotsParticipateInSublease($rpcParams['record_id']);
                }

                $params = [
                    'tablename' => $UserDbController->DbHandler->tableContracts,
                    'where' => [
                        'id' => $rpcParams['record_id'],
                    ],
                ];

                if (1 == $rpcParams['contract_type']) {
                    $params['mainData'] = [
                        'c_num' => $rpcParams['contract_number'],
                        'c_date' => $rpcParams['contract_date'],
                        'nm_usage_rights' => $rpcParams['contract_type'],
                        'sv_num' => $rpcParams['sv_num'],
                        'start_date' => $rpcParams['contract_start_date'],
                        'due_date' => null,
                        'farming_id' => $rpcParams['contract_farming'],
                        'na_num' => $rpcParams['na_num'],
                        'tom' => $rpcParams['tom'],
                        'delo' => $rpcParams['delo'],
                        'court' => $rpcParams['court'],
                        'comment' => $rpcParams['comment'],
                        'renta' => null,
                        'agg_type' => null,
                    ];
                } elseif (2 == $rpcParams['contract_type'] || 3 == $rpcParams['contract_type'] || 5 == $rpcParams['contract_type']) {
                    $params['mainData'] = [
                        'c_num' => $rpcParams['contract_number'],
                        'c_date' => $rpcParams['contract_date'],
                        'nm_usage_rights' => $rpcParams['contract_type'],
                        'sv_num' => $rpcParams['sv_num'],
                        'osz_num' => $rpcParams['osz_num'],
                        'start_date' => $rpcParams['contract_start_date'],
                        'due_date' => $rpcParams['contract_due_date'],
                        'farming_id' => $rpcParams['contract_farming'],
                        'renta' => $rpcParams['renta'],
                        'comment' => $rpcParams['comment'],
                        'na_num' => $rpcParams['na_num'],
                        'tom' => $rpcParams['tom'],
                        'delo' => $rpcParams['delo'],
                        'court' => $rpcParams['court'],
                        'agg_type' => null,
                    ];

                    if (array_key_exists('contract_group', $rpcParams)) {
                        $params['mainData']['group'] = is_string($rpcParams['contract_group']) && strlen($rpcParams['contract_group']) > 0
                            ? $rpcParams['contract_group']
                            : null;
                    }

                    if ('' == $rpcParams['renta']) {
                        $params['mainData']['renta'] = 0;
                    }
                    if ('' != $rpcParams['pd_day'] && '' != $rpcParams['pd_month']) {
                        $params['mainData']['payday'] = $rpcParams['pd_day'] . '-' . $rpcParams['pd_month'];
                    }
                    if (5 != $rpcParams['contract_type']) {
                        $params['mainData']['is_declaration_subleased'] = $rpcParams['is_declaration_subleased'] ? 'true' : 'false';
                        $params['mainData']['is_closed_for_editing'] = $rpcParams['is_closed_for_editing'] ? 'true' : 'false';
                    }
                } elseif (4 == $rpcParams['contract_type']) {
                    $params['mainData'] = [
                        'c_num' => $rpcParams['contract_number'],
                        'c_date' => $rpcParams['contract_date'],
                        'nm_usage_rights' => $rpcParams['contract_type'],
                        'sv_num' => '',
                        'start_date' => $rpcParams['contract_start_date'],
                        'due_date' => $rpcParams['contract_due_date'],
                        'farming_id' => $rpcParams['contract_farming'],
                        'comment' => $rpcParams['comment'],
                        'renta' => null,
                        'agg_type' => $rpcParams['contract_agg_type'],
                        'na_num' => $rpcParams['na_num'],
                        'tom' => $rpcParams['tom'],
                        'delo' => $rpcParams['delo'],
                        'court' => $rpcParams['court'],
                    ];
                }

                if ('' != $rpcParams['sv_date']) {
                    $params['mainData']['sv_date'] = $rpcParams['sv_date'];
                } else {
                    $params['mainData']['sv_date'] = null;
                }

                if ('' != $rpcParams['osz_date']) {
                    $params['mainData']['osz_date'] = $rpcParams['osz_date'];
                } else {
                    $params['mainData']['osz_date'] = null;
                }

                $options = [
                    'tablename' => $UserDbController->DbHandler->contractsPlotsRelTable,
                    'where' => [
                        'contract_id' => ['column' => 'contract_id', 'compare' => '=', 'value' => $rpcParams['record_id']],
                    ],
                ];

                $plots = $UserDbController->getItemsByParams($options);

                $totalAreaForRent = 0;
                foreach ($plots as $plot) {
                    $totalAreaForRent += $plot['area_for_rent'];
                    $options = [
                        'tablename' => $UserDbController->DbHandler->contractsPlotsRelTable,
                        'mainData' => [
                            'contract_end_date' => 1 == $rpcParams['contract_type'] ? null : $rpcParams['contract_due_date'],
                        ],
                        'where' => [
                            'id' => $plot['id'],
                        ],
                    ];
                    $UserDbController->editItem($options);
                }

                if ('' != $rpcParams['overall_renta']) {
                    if ($totalAreaForRent) {
                        $params['mainData']['renta'] = $rpcParams['overall_renta'] / $totalAreaForRent;
                    }
                    $params['mainData']['overall_renta'] = $rpcParams['overall_renta'];
                    $UserDbContractsController->removeRentPerPlot($rpcParams['record_id']);
                } else {
                    $params['mainData']['overall_renta'] = null;
                }

                $UserDbController->editItem($params);

                $UsersController->groupLog($this->User->Name, $this->User->UserID, $this->User->GroupID, $this->module, $this->service_id, __METHOD__, $params, $oldContractData, 'edits contract');

                $deleteOptions = [
                    'tablename' => $UserDbController->DbHandler->contractsRentsRelTable,
                    'id_name' => 'contract_id',
                    'id_string' => $rpcParams['record_id'],
                ];
                $UserDbController->deleteItemsByParams($deleteOptions);
                $rentaParams = [
                    'tablename' => $UserDbController->DbHandler->contractsRentsRelTable,
                    'mainData' => [
                        'contract_id' => $rpcParams['record_id'],
                    ],
                ];

                foreach ($rpcParams['additionalRentas'] as $renta) {
                    $currentAdditionalRenta = (object) $renta;
                    if ((null == $currentAdditionalRenta->type || 0 == $currentAdditionalRenta->type) && (0 == $currentAdditionalRenta->value || '' == $currentAdditionalRenta->value)) {
                        continue;
                    }

                    $rentaParams['mainData']['renta_id'] = $currentAdditionalRenta->type;
                    $rentaParams['mainData']['renta_value'] = $currentAdditionalRenta->value;

                    $UserDbController->addItem($rentaParams);
                }
            } else {
                // creating new contract
                $params = [
                    'tablename' => $UserDbController->DbHandler->tableContracts,
                ];
                $rentaParams = [
                    'tablename' => $UserDbController->DbHandler->contractsRentsRelTable,
                ];

                if (1 == $rpcParams['contract_type']) {
                    $params['mainData'] = [
                        'c_num' => $rpcParams['contract_number'],
                        'c_date' => $rpcParams['contract_date'],
                        'nm_usage_rights' => $rpcParams['contract_type'],
                        'sv_num' => $rpcParams['sv_num'],
                        'start_date' => $rpcParams['contract_start_date'],
                        'farming_id' => $rpcParams['contract_farming'],
                        'na_num' => $rpcParams['na_num'],
                        'tom' => $rpcParams['tom'],
                        'delo' => $rpcParams['delo'],
                        'court' => $rpcParams['court'],
                        'comment' => $rpcParams['comment'],
                    ];
                } elseif (2 == $rpcParams['contract_type'] || 3 == $rpcParams['contract_type'] || 5 == $rpcParams['contract_type']) {
                    $params['mainData'] = [
                        'c_num' => $rpcParams['contract_number'],
                        'c_date' => $rpcParams['contract_date'],
                        'nm_usage_rights' => $rpcParams['contract_type'],
                        'sv_num' => $rpcParams['sv_num'],
                        'osz_num' => $rpcParams['osz_num'],
                        'start_date' => $rpcParams['contract_start_date'],
                        'due_date' => $rpcParams['contract_due_date'],
                        'farming_id' => $rpcParams['contract_farming'],
                        'renta' => $rpcParams['renta'],
                        'comment' => $rpcParams['comment'],
                        'na_num' => $rpcParams['na_num'],
                        'tom' => $rpcParams['tom'],
                        'delo' => $rpcParams['delo'],
                        'court' => $rpcParams['court'],
                    ];

                    if (isset($rpcParams['contract_group']) && '' != $rpcParams['contract_group']) {
                        $params['mainData']['"group"'] = $rpcParams['contract_group'];
                    }

                    if ('' == $rpcParams['renta']) {
                        $params['mainData']['renta'] = 0;
                    }
                    if ('' != $rpcParams['pd_day'] && '' != $rpcParams['pd_month']) {
                        $params['mainData']['payday'] = $rpcParams['pd_day'] . '-' . $rpcParams['pd_month'];
                    }
                    if (5 != $rpcParams['contract_type']) {
                        $params['mainData']['is_declaration_subleased'] = $rpcParams['is_declaration_subleased'];
                        $params['mainData']['is_closed_for_editing'] = $rpcParams['is_closed_for_editing'];
                    }
                } elseif (4 == $rpcParams['contract_type']) {
                    $params['mainData'] = [
                        'c_num' => $rpcParams['contract_number'],
                        'c_date' => $rpcParams['contract_date'],
                        'nm_usage_rights' => $rpcParams['contract_type'],
                        'start_date' => $rpcParams['contract_start_date'],
                        'due_date' => $rpcParams['contract_due_date'],
                        'farming_id' => $rpcParams['contract_farming'],
                        'comment' => $rpcParams['comment'],
                        'agg_type' => $rpcParams['contract_agg_type'],
                        'na_num' => $rpcParams['na_num'],
                        'tom' => $rpcParams['tom'],
                        'delo' => $rpcParams['delo'],
                        'court' => $rpcParams['court'],
                    ];
                }

                if ('' != $rpcParams['sv_date']) {
                    $params['mainData']['sv_date'] = $rpcParams['sv_date'];
                }
                if ('' != $rpcParams['osz_date']) {
                    $params['mainData']['osz_date'] = $rpcParams['osz_date'];
                }

                if ('' != $rpcParams['overall_renta']) {
                    $params['mainData']['overall_renta'] = $rpcParams['overall_renta'];
                } else {
                    $params['mainData']['overall_renta'] = null;
                }

                // Get copied contract plots
                $options = [
                    'tablename' => $UserDbController->DbHandler->contractsPlotsRelTable,
                    'where' => [
                        'contract_id' => ['column' => 'contract_id', 'compare' => '=', 'value' => $rpcParams['copy_id']],
                        'annex_action' => ['column' => 'annex_action', 'compare' => '=', 'value' => 'added'],
                    ],
                ];

                $plots = $UserDbController->getItemsByParams($options);

                // when trying to copy contract check contracts plots exists in another contract with overlapping period
                if (array_key_exists('copy_id', $rpcParams) && $rpcParams['copy_id'] > 0) {
                    $plotsWithActiveContracts = $UserDbContractsController->getNotAvailableContractsPlots(
                        $rpcParams['contract_start_date'],
                        $rpcParams['contract_due_date'],
                        [],
                        ['editContract' => true],
                        array_column($plots, 'plot_id'),
                        [],
                        null
                    );

                    $UserDbContractsController->validatePlotAreas($plots, $plotsWithActiveContracts);
                }

                $recordID = $UserDbController->addItem($params);
                if (count($rpcParams['additionalRentas']) > 0) {
                    $rentaParams['mainData']['contract_id'] = $recordID;

                    foreach ($rpcParams['additionalRentas'] as $renta) {
                        $currentAdditionalRenta = (object) $renta;
                        if ((null == $currentAdditionalRenta->type || 0 == $currentAdditionalRenta->type) && (0 == $currentAdditionalRenta->value || '' == $currentAdditionalRenta->value)) {
                            continue;
                        }

                        $rentaParams['mainData']['renta_id'] = $currentAdditionalRenta->type;
                        $rentaParams['mainData']['renta_value'] = $currentAdditionalRenta->value;

                        $UserDbController->addItem($rentaParams);
                    }
                }

                if (0 == strlen($rpcParams['copy_id'])) {
                    $UserDbController->enableRentaMatViewTriggers();

                    $UsersController->groupLog($this->User->Name, $this->User->UserID, $this->User->GroupID, $this->module, $this->service_id, __METHOD__, $params['mainData'], $recordID, 'add contract');

                    return $recordID;
                }
                $UsersController->groupLog($this->User->Name, $this->User->UserID, $this->User->GroupID, $this->module, $this->service_id, __METHOD__, $params['mainData'], $recordID, 'copy contract');

                $this->copyContractRelData($rpcParams['copy_id'], $recordID, $rpcParams['contract_due_date'], $rpcParams['contract_type'], $rpcParams['copy_contract_files']);

                if ('' != $rpcParams['overall_renta']) {
                    $UserDbContractsController->removeRentPerPlot($recordID);
                }

                $UserDbController->enableRentaMatViewTriggers();

                if ($paymentStatus) {
                    return $paymentStatus;
                }

                return $recordID;
            }
        } catch (MTRpcException $ex) {
            $UserDbController->enableRentaMatViewTriggers();

            throw $ex;
        }
    }

    /**
     * Deletes the selected contract.
     *
     * @api-method deleteContracts
     *
     * @param array $rpcParam
     *
     * @throws MTRpcException
     *
     * @return array
     *               {
     *               #item boolean hasContractsOwnWriteRights
     *               #item integer contractType
     *               }
     */
    public function deleteContracts($rpcParam)
    {
        if (!$rpcParam['contract_id']) {
            return [];
        }

        $UserDbContractsController = new UserDbContractsController($this->User->Database);
        $UserDbController = new UserDbController($this->User->Database);
        $UsersController = new UsersController('Users');
        $UserDbPaymentsController = new UserDbPaymentsController($this->User->Database);

        $oldOptions = [
            'return' => [
                'c.*',
                'cpr.plot_id',
            ],
            'tablename' => $UserDbController->DbHandler->tableContracts . ' c',
            'innerjoin' => [
                'table' => $UserDbController->DbHandler->contractsPlotsRelTable . ' cpr',
                'condition' => ' ON (c.id = cpr.contract_id)',
            ],
            'where' => [
                'id' => ['column' => 'c.id', 'compare' => '=', 'value' => $rpcParam['contract_id']],
            ],
        ];
        $oldData = $UserDbController->getItemsByParams($oldOptions);

        // No Rights to operate with "Договори за собственост"
        if (Config::CONTRACT_TYPE_OWN == $oldData[0]['nm_usage_rights'] && !$this->User->HasContractsOwnWriteRights) {
            return $UserDbContractsController->setResponseDataContracts($oldData[0]['nm_usage_rights'], $this->User->HasContractsOwnWriteRights);
        }

        $isFromSublease = $UserDbContractsController->isContractFromSublease($rpcParam['contract_id']);

        if ($isFromSublease) {
            throw new MTRpcException('CANNOT_MODIFY_FROM_SUBLEASE_CONTRACT', -33752);
        }

        if (Config::CONTRACT_TYPE_OWN == $oldData[0]['nm_usage_rights']) {
            $this->validateSalesContractRelations(
                [
                    'contract_id' => $rpcParam['contract_id'],
                ],
                $UserDbContractsController
            );
        }

        $additionalParams = [];
        if (!empty($oldData[0]['parent_id'])) {
            $additionalParams = [
                'parent_id' => $oldData[0]['parent_id'],
                'start_date' => $oldData[0]['start_date'],
                'due_date' => $oldData[0]['due_date'],
            ];
        }
        $UserDbPaymentsController->hasPaymentRestriction($rpcParam['contract_id'], $additionalParams);

        // Disable it in order to make faster adding plots into contracts. It is enable again below
        $UserDbController->disableRentaMatViewTriggers();

        $subleasedPlots = $UserDbController->getSubleasedPlotsOfContract(['contract_id' => $rpcParam['contract_id']]);
        $hasSubleasedPlots = count($subleasedPlots);

        if (!$rpcParam['confirm'] && $hasSubleasedPlots) {
            throw new MTRpcException('Договора не може да бъде изтрит, защото съдържа имот(и) ' . (implode(',', array_column($subleasedPlots, 'res'))) . ', които участват в активни или бъдещи договори за преотдаване.', -33754);
        }

        $transaction = $UserDbController->startTransaction();

        try {
            // delete natural rents
            $deleteOptions = [
                'tablename' => $UserDbController->DbHandler->contractsRentsRelTable,
                'id_name' => 'contract_id',
                'id_string' => $rpcParam['contract_id'],
            ];
            $UserDbController->deleteItemsByParams($deleteOptions);

            if ($hasSubleasedPlots) {
                // delete subleases plots contract rels
                $UserDbController->deleteItemsByParams([
                    'tablename' => $UserDbController->DbHandler->tableSubleasesPlotsContractsRel,
                    'id_name' => 'pc_rel_id',
                    'id_string' => implode(',', array_column($subleasedPlots, 'sub_rels')),
                ]);
                // delete subleases plots areas
                $UserDbController->deleteItemsByParams([
                    'tablename' => $UserDbController->DbHandler->tableSubleasesPlotsArea,
                    'id_name' => 'sublease_id',
                    'id_string' => implode(',', array_column($subleasedPlots, 'sublease_id')),
                    'where' => [
                        'plot_id' => ['column' => 'plot_id', 'compare' => 'IN', 'value' => array_column($subleasedPlots, 'plot_id')],
                    ],
                ]);
            }

            $options = [
                'return' => [
                    'cp.plot_id',
                    'max(file_id) as file_id',
                    'max(date_uploaded) as date_uploaded',
                    'kvs.ekate',
                    'kvs.kad_ident',
                    'c.*',
                ],
                'tablename' => $UserDbController->DbHandler->tableContracts . ' as c',
                'where' => [
                    'id' => ['column' => 'id', 'prefix' => 'c', 'compare' => '=', 'value' => $rpcParam['contract_id']],
                ],
                'innerjoin' => [
                    'table' => $UserDbController->DbHandler->contractsPlotsRelTable . ' cp',
                    'condition' => '  ON cp.contract_id = c.id',
                ],
                'leftjoin' => [
                    'table' => $UserDbController->DbHandler->tableKVS . ' kvs',
                    'condition' => '  ON cp.plot_id = kvs.gid',
                ],
                'joins' => [
                    "LEFT JOIN dblink('host=" . DEFAULT_DB_HOST . ' port=' . DEFAULT_DB_PORT . ' user=' . DEFAULT_DB_USERNAME . ' password=' . DEFAULT_DB_PASSWORD . ' dbname=' . DEFAULT_DB_DATABASE . "',
                    'SELECT id AS file_id, ekate AS ekatte_code, date_uploaded, group_id, shape_type FROM su_users_files') AS files (file_id integer, ekatte_code text, date_uploaded timestamp, group_id integer, shape_type integer) 
                    ON kvs.ekate = files.ekatte_code AND files.group_id = {$this->User->GroupID} AND files.shape_type = " . Config::LAYER_TYPE_KVS_OSZ,
                ],
                'group' => 'cp.plot_id, c.id, kvs.ekate, kvs.kad_ident',
            ];

            $oldData = $UserDbController->getItemsByParams($options);

            // delete contract plots rels
            $UserDbController->deleteItemsByParams([
                'tablename' => $UserDbController->DbHandler->contractsPlotsRelTable,
                'id_name' => 'contract_id',
                'id_string' => $rpcParam['contract_id'],
            ]);

            $options = [
                'tablename' => $UserDbController->DbHandler->tableContracts,
                'id_string' => $rpcParam['contract_id'],
            ];
            $UserDbController->deleteItemsByParams($options);

            // delete annexes
            $options = [
                'tablename' => $UserDbController->DbHandler->tableContracts,
                'id_name' => 'parent_id',
                'id_string' => $rpcParam['contract_id'],
                'where' => [
                    'is_annex' => ['column' => 'is_annex', 'compare' => '=', 'value' => 'TRUE'],
                ],
            ];
            $UserDbController->deleteItemsByParams($options);

            $UsersController->groupLog($this->User->Name, $this->User->UserID, $this->User->GroupID, $this->module, $this->service_id, __METHOD__, $oldData, [], 'delete contract');

            $transaction->commit();

            // try resolve kvs contracts update outside transaction because nested transactions
            if ($oldData[0]['file_id']) {
                $this->tryResolveContracts(
                    $oldData[0]['file_id'],
                    $oldData[0]['ekate'],
                    array_map(function ($data) {
                        return [
                            'kad_ident' => $data['kad_ident'],
                            'edit_active_from' => (DateTime::createFromFormat('Y-m-d H:i:s.u', $data['date_uploaded']))->modify('-1 day')->format('Y-m-d'),
                        ];
                    }, $oldData)
                );
            }

            return $UserDbContractsController->setResponseDataContracts($contractType, $this->User->HasContractsOwnWriteRights);
        } catch (Exception $e) {
            $transaction->rollBack();

            throw $e;
        } finally {
            $UserDbController->enableRentaMatViewTriggers();
        }
    }

    /**
     * Changes the active status of the selected contract
     * Throws MTRpcException if the user has no rights to operate with the selected contract.
     *
     * @api-method changeActiveStatus
     *
     * @param array $rpcParams
     *                         {
     *                         #item integer id
     *                         #item boolean status
     *                         #item string comment
     *                         }
     *
     * @throws MTRpcException
     *
     * @return array
     *               {
     *               #item boolean hasContractsOwnWriteRights
     *               #item integer contractType
     *               }
     */
    public function changeActiveStatus($rpcParams)
    {
        $contract_id = $rpcParams['id'];

        $UserDbContractsController = new UserDbContractsController($this->User->Database);
        $UserDbController = new UserDbController($this->User->Database);
        $UsersController = new UsersController('Users');
        $UserDbPaymentsController = new UserDbPaymentsController($this->User->Database);

        $oldOptions = [
            'return' => [
                'c.*',
                'cpr.plot_id',
            ],
            'tablename' => $UserDbController->DbHandler->tableContracts . ' c',
            'innerjoin' => [
                'table' => $UserDbController->DbHandler->contractsPlotsRelTable . ' cpr',
                'condition' => ' ON (c.id = cpr.contract_id)',
            ],
            'where' => [
                'id' => ['column' => 'c.id', 'compare' => '=', 'value' => $contract_id],
            ],
        ];
        $oldData = $UserDbController->getItemsByParams($oldOptions);

        // No Rights to operate with "Договори за собственост"
        if (Config::CONTRACT_TYPE_OWN == $oldData[0]['nm_usage_rights'] && !$this->User->HasContractsOwnWriteRights) {
            throw new MTRpcException('invalid_user_contracts_rights_own_contracts', -33200);
        }

        $isFromSublease = $UserDbContractsController->isContractFromSublease($contract_id);
        if ($isFromSublease) {
            throw new MTRpcException('CANNOT_MODIFY_FROM_SUBLEASE_CONTRACT', -33752);
        }

        if (false == $rpcParams['status'] && Config::CONTRACT_TYPE_OWN == $oldData[0]['nm_usage_rights']) {
            $this->validateSalesContractRelations(
                [
                    'contract_id' => $contract_id,
                ],
                $UserDbContractsController
            );
        }

        $additionalParams = [];
        if (!empty($oldData[0]['parent_id'])) {
            $additionalParams = [
                'parent_id' => $oldData[0]['parent_id'],
                'start_date' => $oldData[0]['start_date'],
                'due_date' => $oldData[0]['due_date'],
            ];
        }
        $UserDbPaymentsController->hasPaymentRestriction($contract_id, $additionalParams);

        if (!empty($oldData) && true === $rpcParams['status']) {
            $excludeContract = null;
            if (!empty($oldData[0]['parent_id'])) {
                $excludeContract = [$oldData[0]['parent_id']];
            }

            // Get contract plots
            $options = [
                'tablename' => $UserDbController->DbHandler->contractsPlotsRelTable,
                'where' => [
                    'contract_id' => ['column' => 'contract_id', 'compare' => '=', 'value' => $oldData[0]['id']],
                    'annex_action' => ['column' => 'annex_action', 'compare' => '=', 'value' => 'added'],
                ],
            ];
            $plots = $UserDbController->getItemsByParams($options);

            // Check contracts plots exist in another contract with overlapping period
            $plotsWithActiveContracts = $UserDbContractsController->getNotAvailableContractsPlots(
                $oldData[0]['start_date'],
                $oldData[0]['due_date'],
                [$oldData[0]['id']],
                ['editContract' => true],
                array_column($plots, 'plot_id'),
                $excludeContract
            );

            $UserDbContractsController->validatePlotAreas($plots, $plotsWithActiveContracts);
        }

        if (!empty($oldData) && true === !$rpcParams['status']) {
            $plotsData = $UserDbContractsController->validatePlotAreasInContracts(array_column($oldData, 'plot_id'), $oldData[0]['start_date'], $oldData[0]['due_date'], $oldData[0]['parent_id']);

            $negativeAreaPlots = array_filter($plotsData, fn ($plot) => $plot['available_area'] < 0);
            if (!empty($negativeAreaPlots)) {
                throw new MTRpcException($plotsData, self::CONTRACTS_AVAILABLE_PLOT_AREA_EXCEPTION);
            }
        }

        $options = [
            'tablename' => $UserDbController->DbHandler->tableContracts,
            'mainData' => [
                'active' => $rpcParams['status'],
                'comment' => $rpcParams['comment'],
            ],
            'where' => [
                'id' => $contract_id,
            ],
        ];

        $UserDbController->editItem($options);

        // change status of annexes for this contract
        $options['where'] = [
            'parent_id' => $contract_id,
        ];
        $UserDbController->editItem($options);

        $newData = [
            'active' => $options['mainData']['active'],
            'comment' => $options['mainData']['comment'],
            'contract_id_or_parent_id' => $contract_id,
        ];
        $UsersController->groupLog($this->User->Name, $this->User->UserID, $this->User->GroupID, $this->module, $this->service_id, __METHOD__, $newData, $oldData, 'Deactivate/Activate contract');

        // setResponseData
        return $UserDbContractsController->setResponseDataContracts($contractType, $this->User->HasContractsOwnWriteRights);
    }

    /**
     * Returns the selected contract data, while changing the operation days of the contract
     * Throws MTRpcException if the user has no rights to operate with the selected contract.
     *
     * @api-method contractCopy
     *
     * @param int $rpcParam
     *
     * @throws MTRpcException
     *
     * @return array
     *               {
     *               #item boolean active
     *               #item integer agg_type
     *               #item date    c_date
     *               #item string  c_num
     *               #item string  comment
     *               #item string  court
     *               #item string  delo
     *               #item date    due_date
     *               #item integer farming_id
     *               #item integer id
     *               #item boolean is_annex
     *               #item boolean is_sublease
     *               #item string  na_num
     *               #item integer nm_usage_rights
     *               #item date    original_due_date
     *               #item float   original_renta
     *               #item float   original_renta_nat
     *               #item integer original_renta_nat_type_id
     *               #item integer parent_id
     *               #item date    payday
     *               #item integer pd_day
     *               #item integer pd_month
     *               #item float   renta
     *               #item float   renta_nat
     *               #item integer renta_nat_type_id
     *               #item date    start_date
     *               #item date    sv_date
     *               #item string  sv_num
     *               #item string  tom
     *               #item array additionalRentas
     *               {
     *               #item integer type
     *               #item float   value
     *               }
     *               }
     */
    public function initContractCopy($rpcParam)
    {
        $UserDbContractsController = new UserDbContractsController($this->User->Database);

        // get contractType
        $contractType = $UserDbContractsController->getContractType($rpcParam);

        // No Rights to operate with "Договори за собственост"
        if (Config::CONTRACT_TYPE_OWN == $contractType && !$this->User->HasContractsOwnWriteRights) {
            throw new MTRpcException('invalid_user_contracts_rights_own_contracts', -33200);
        }

        $isFromSublease = $UserDbContractsController->isContractFromSublease($rpcParam);
        if ($isFromSublease) {
            throw new MTRpcException('CANNOT_MODIFY_FROM_SUBLEASE_CONTRACT', -33752);
        }

        $results = $this->getContractsToCopy(['contract_ids' => [$rpcParam]]);

        $rentaOptions = [
            'where' => [
                'id' => ['column' => 'contract_id', 'compare' => '=', 'value' => $results[0]['id']],
            ],
        ];
        $rentaResult = $UserDbContractsController->getContractRentsData($rentaOptions);
        $rentaResultCount = count($rentaResult);
        if ($rentaResultCount > 0) {
            for ($i = 0; $i < $rentaResultCount; $i++) {
                $results[0]['additionalRentas'][$i] = $rentaResult[$i];
            }
        }

        $newDates = $this->replaceContractDates($results[0]);

        $new_date = date('Y-m-d', strtotime($newDates['contract']['contract_date']));
        $new_start_date = date('Y-m-d', strtotime($newDates['contract']['contract_start_date']));
        $new_due_date = date('Y-m-d', strtotime($newDates['contract']['contract_due_date']));

        $results[0]['c_date'] = $new_date;
        $results[0]['start_date'] = $new_start_date;
        $results[0]['due_date'] = $new_due_date;

        $results[0]['is_copy'] = true;
        if ($results[0]['payday']) {
            $results[0]['payday'] = explode('-', $results[0]['payday']);
            $results[0]['pd_day'] = $payday[0];
            $results[0]['pd_month'] = $payday[1];
        } else {
            $results[0]['payday'] = ['', ''];
            $results[0]['pd_day'] = '';
            $results[0]['pd_month'] = '';
        }

        return $results[0];
    }

    /**
     * Return data for editing contract.
     *
     * @api-method markForEdit
     *
     * @param int $rpcParam
     *
     * @throws MTRpcException
     *
     * @return array
     *               {
     *               #item boolean active
     *               #item integer agg_type
     *               #item date    c_date
     *               #item string  c_num
     *               #item string  comment
     *               #item string  court
     *               #item string  delo
     *               #item date    due_date
     *               #item integer farming_id
     *               #item integer id
     *               #item boolean is_annex
     *               #item boolean is_sublease
     *               #item string  na_num
     *               #item integer nm_usage_rights
     *               #item date    original_due_date
     *               #item float   original_renta
     *               #item float   original_renta_nat
     *               #item integer original_renta_nat_type_id
     *               #item integer parent_id
     *               #item date    payday
     *               #item integer pd_day
     *               #item integer pd_month
     *               #item float   renta
     *               #item float   renta_nat
     *               #item integer renta_nat_type_id
     *               #item date    start_date
     *               #item date    sv_date
     *               #item string  sv_num
     *               #item string  tom
     *               #item array additionalRentas
     *               #item boolean is_declaration_subleased
     *               {
     *               #item integer type
     *               #item float   value
     *               }
     *               }
     */
    public function markForEdit($rpcParam)
    {
        $UserDbContractsController = new UserDbContractsController($this->User->Database);

        // get contractType
        $contractType = $UserDbContractsController->getContractType($rpcParam);

        // No Rights to operate with "Договори за собственост"
        if (Config::CONTRACT_TYPE_OWN == $contractType && !$this->User->HasContractsOwnWriteRights) {
            throw new MTRpcException('invalid_user_contracts_rights_own_contracts', -33200);
        }

        $isFromSublease = $UserDbContractsController->isContractFromSublease($rpcParam);
        if ($isFromSublease) {
            throw new MTRpcException('CANNOT_MODIFY_FROM_SUBLEASE_CONTRACT', -33752);
        }

        $options = [
            'return' => [
                '*',
                'date(c_date) as c_converted_date',
                'date(start_date) as converted_start_date',
                'date(sv_date) as converted_sv_date',
                'date(due_date) as converted_due_date',
            ],
            'where' => [
                'id' => ['column' => 'id', 'compare' => '=', 'value' => $rpcParam],
            ],
        ];

        $result = $UserDbContractsController->getContractsData($options, false);

        $hasFarmingPermission = $this->User->hasPermissionTo(ObjectPermissions::PERMISSION_WRITE, UserFarmings::class, $result[0]['farming_id']);
        if (false === $hasFarmingPermission) {
            throw new MTRpcException('NO_CONTRACT_PERMISSION', -33657);
        }

        if ($result[0]['payday']) {
            $result[0]['payday'] = explode('-', $result[0]['payday']);
            $result[0]['pd_day'] = $result[0]['payday'][0];
            $result[0]['pd_month'] = $result[0]['payday'][1];
        } else {
            $result[0]['payday'] = ['', ''];
            $result[0]['pd_day'] = '';
            $result[0]['pd_month'] = '';
        }

        if (!is_null($result[0]['c_date'])) {
            $result[0]['c_date'] = $this->formatDate($result[0]['c_date'], 'Y-m-d');
        }

        if (!is_null($result[0]['sv_date'])) {
            $result[0]['sv_date'] = $this->formatDate($result[0]['sv_date'], 'Y-m-d');
        }

        if (!is_null($result[0]['osz_date'])) {
            $result[0]['osz_date'] = $this->formatDate($result[0]['osz_date'], 'Y-m-d');
        }

        if (!is_null($result[0]['start_date'])) {
            $result[0]['start_date'] = $this->formatDate($result[0]['start_date'], 'Y-m-d');
        }

        if (!is_null($result[0]['due_date'])) {
            $result[0]['due_date'] = $this->formatDate($result[0]['due_date'], 'Y-m-d');
        }

        $rentaOptions = [
            'where' => [
                'id' => ['column' => 'contract_id', 'compare' => '=', 'value' => $rpcParam],
            ],
        ];
        $rentaResult = $UserDbContractsController->getContractRentsData($rentaOptions);
        $rentaResultCount = count($rentaResult);
        if ($rentaResultCount > 0) {
            for ($i = 0; $i < $rentaResultCount; $i++) {
                $result[0]['additionalRentas'][$i] = $rentaResult[$i];
            }
        }

        $result[0]['hasContractsOwnWriteRights'] = $this->User->HasContractsOwnWriteRights;
        $result[0]['contractType'] = $result[0]['nm_usage_rights'];
        $result[0]['isEdit'] = true;

        return $result[0];
    }

    /**
     * Add annex to the current selected contract
     * Throws MTRpcException if annex with the same period already exists.
     *
     * @api-method addAnnex
     *
     * @param array $rpcParams
     *                         {
     *                         #item integer annex_parent_id
     *                         #item integer annex_start_date
     *                         #item integer annex_due_date
     *                         #item integer annex_num
     *                         #item integer annex_date
     *                         #item integer annex_sv_num
     *                         #item integer farming_id
     *                         #item integer annex_comment
     *                         #item integer nm_usage_rights
     *                         #item integer annex_sv_date
     *                         #item integer annex_pd_day
     *                         #item integer annex_renta_nat_type
     *                         #item integer annex_renta_nat
     *                         #item integer annex_renta
     *                         #item integer additionalRentas
     *                         {
     *                         #item integer annex_renta_nat_type
     *                         #item integer annex_renta_nat
     *                         }
     *
     *        		#item array added_plots_data
     *        			{
     *        		 		#item float   document_area
     *        		 		#item float   contract_area
     *        		 		#item integer gid
     *        			}
     *        		#item array removed_plots_gids
     *        			{
     *        				#item integer gid
     *        			}
     *        }
     *
     * @throws MTRpcException
     *
     * @return array|bool
     */
    public function addAnnex($rpcParams)
    {
        $UserDbController = new UserDbController($this->User->Database);
        $UserDbContractsController = new UserDbContractsController($this->User->Database);
        $UsersController = new UsersController('Users');
        $UserDbPaymentsController = new UserDbPaymentsController($this->User->Database);
        $contract_id = $rpcParams['annex_parent_id'];
        if (!$contract_id) {
            return [];
        }

        $oldOptions = [
            'tablename' => $UserDbController->DbHandler->tableContracts . ' c',
            'return' => ['c.*'],
            'where' => [
                'id' => ['column' => 'id', 'compare' => '=', 'prefix' => 'c', 'value' => $contract_id],
            ],
            'group' => 'c.id',
        ];

        $oldContractData = $UserDbController->getItemsByParams($oldOptions, false, false);

        // Restrictions when editing a contract/annex if a payment is made. Restriction only if the following fields are changed: start_date, due_date, farming_id, renta and renta_natura [GPS-3842]
        if (
            date('Y-m-d', strtotime($oldContractData[0]['start_date'])) >= date('Y-m-d', strtotime($rpcParams['annex_start_date']))
            || date('Y-m-d', strtotime($oldContractData[0]['due_date'])) <= date('Y-m-d', strtotime($rpcParams['annex_due_date']))
        ) {
            $UserDbPaymentsController->hasPaymentRestriction($oldContractData[0]['id'], [
                'parent_id' => $oldContractData[0]['id'],
                'start_date' => $rpcParams['annex_start_date'],
                'due_date' => $rpcParams['annex_due_date'],
            ]);
        }

        // No Rights to operate with "Договори за собственост"
        if (Config::CONTRACT_TYPE_OWN == $oldContractData[0]['nm_usage_rights'] || Config::CONTRACT_TYPE_AGREEMENT == $oldContractData[0]['nm_usage_rights']) {
            throw new MTRpcException('CANNOT_ANNEX_OWN_OR_AGREEMENT_CONTRACT', -33205);
        }
        $isFromSublease = $UserDbContractsController->isContractFromSublease($contract_id);
        if ($isFromSublease) {
            throw new MTRpcException('CANNOT_MODIFY_FROM_SUBLEASE_CONTRACT', -33752);
        }

        foreach ($rpcParams['added_plots_data'] as $anex_plot_data) {
            if ($anex_plot_data['area_for_rent'] > $anex_plot_data['contract_area']) {
                throw new MTRpcException('WRONG_PLOT_AREA_FOR_RENT_TOO_LARGE', -33226);
            }
        }

        $contract = $UserDbController->getItemsByParams([
            'tablename' => $UserDbController->DbHandler->tableContracts,
            'where' => [
                'parent_id' => ['column' => 'id', 'compare' => '=', 'value' => $contract_id],
            ],
        ]);

        if (true == current($contract)['is_annex']) {
            throw new MTRpcException('INVALID_ANNEX_CONTRACTS', -33201);
        }

        if (!empty($rpcParams['added_plots_data'])) {
            // Check contracts plots exist in another contract with overlapping period
            $plotsWithActiveContracts = $UserDbContractsController->getNotAvailableContractsPlots(
                $rpcParams['annex_start_date'],
                $rpcParams['annex_due_date'],
                [],
                [],
                array_column($rpcParams['added_plots_data'], 'plot_id'),
                [$rpcParams['annex_parent_id']], // exclude parent contract from check because it is possible to have same plots in parent contract
            );
            $UserDbContractsController->validatePlotAreas($rpcParams['added_plots_data'], $plotsWithActiveContracts);
        }

        // check if has annex with same period
        $options = [
            'tablename' => $UserDbController->DbHandler->tableContracts,
            'where' => [
                'parent_id' => ['column' => 'parent_id', 'compare' => '=', 'value' => $contract_id],
            ],
        ];

        $results = $UserDbController->getItemsByParams($options);

        $resultsCount = count($results);
        for ($i = 0; $i < $resultsCount; $i++) {
            if (!(date('Y-m-d', strtotime($results[$i]['due_date'])) < date('Y-m-d', strtotime($rpcParams['annex_start_date']))
                    || date('Y-m-d', strtotime($results[$i]['start_date'])) > date('Y-m-d', strtotime($rpcParams['annex_due_date'])))
                    && true == $results[$i]['active']) {
                throw new MTRpcException('ANNEXES_INVALID_ANNEX_DATE', -33202);
            }
        }
        // END check if has annex with same period

        // get owners data for contract plots
        $options = [
            'return' => ['plot_id', 'po.*'],
            'where' => [
                'contract_id' => ['column' => 'contract_id', 'compare' => '=', 'prefix' => 'pc', 'value' => $rpcParams['annex_id'] ?? $contract_id],
            ],
        ];

        $results = $UserDbContractsController->getPlotOwnerRelData($options);
        $resultsCount = count($results);
        $owner_results_by_plot = [];
        for ($i = 0; $i < $resultsCount; $i++) {
            $owner_results_by_plot[$results[$i]['plot_id']][] = $results[$i];
        }

        // get farming data for contract plots
        $options = [
            'return' => ['plot_id', 'pf.*'],
            'where' => [
                'contract_id' => ['column' => 'contract_id', 'compare' => '=', 'prefix' => 'pc', 'value' => $contract_id],
            ],
        ];

        $farming_results = $UserDbContractsController->getPlotFarmingRelData($options);
        $farmingCount = count($farming_results);

        $farming_results_by_plot = [];
        for ($i = 0; $i < $farmingCount; $i++) {
            $farming_results_by_plot[$farming_results[$i]['plot_id']][] = $farming_results[$i];
        }

        $options = [
            'tablename' => $UserDbController->DbHandler->tableContracts,
            'where' => [
                'contract_id' => ['column' => 'id', 'compare' => '=', 'value' => $contract_id],
            ],
        ];

        $result = $UserDbController->getItemsByParams($options);
        $contractData = $result[0];

        // add annex
        $options = [
            'tablename' => $UserDbController->DbHandler->tableContracts,
            'mainData' => [
                'c_num' => $rpcParams['annex_num'],
                'c_date' => $rpcParams['annex_date'],
                'start_date' => $rpcParams['annex_start_date'],
                'sv_num' => $rpcParams['annex_sv_num'],
                'osz_num' => $rpcParams['annex_osz_num'],
                'due_date' => $rpcParams['annex_due_date'],
                'farming_id' => $contractData['farming_id'],
                'comment' => $rpcParams['annex_comment'],
                'parent_id' => $contract_id,
                'nm_usage_rights' => $contractData['nm_usage_rights'],
                'is_annex' => 1,
                '"group"' => $contractData['group'],
            ],
        ];

        if ('' != $rpcParams['annex_sv_date']) {
            $options['mainData']['sv_date'] = $rpcParams['annex_sv_date'];
        }

        if ('' != $rpcParams['annex_osz_date']) {
            $options['mainData']['osz_date'] = $rpcParams['annex_osz_date'];
        }

        if ('' != $rpcParams['annex_pd_day'] && '' != $rpcParams['annex_pd_month']) {
            $options['mainData']['payday'] = $rpcParams['annex_pd_day'] . '-' . $rpcParams['annex_pd_month'];
        }

        if ('' !== $rpcParams['annex_renta']) {
            $options['mainData']['renta'] = $rpcParams['annex_renta'];
        }

        if ('' !== $rpcParams['overall_renta']) {
            $options['mainData']['overall_renta'] = $rpcParams['overall_renta'];
        }

        try {
            $annex_id = $UserDbController->addItem($options);

            if (count($rpcParams['additionalRentas']) > 0) {
                $rentaParams = [
                    'tablename' => $UserDbController->DbHandler->contractsRentsRelTable,
                    'mainData' => [
                        'contract_id' => $annex_id,
                    ],
                ];

                foreach ($rpcParams['additionalRentas'] as $renta) {
                    $currentAdditionalRenta = (object) $renta;
                    if ((null == $currentAdditionalRenta->type || 0 == $currentAdditionalRenta->type)) {
                        continue;
                    }

                    $rentaParams['mainData']['renta_id'] = $currentAdditionalRenta->type;
                    $rentaParams['mainData']['renta_value'] = $currentAdditionalRenta->value;

                    $rents[] = [
                        'contractsRentsRelation' => $UserDbController->addItem($rentaParams),
                        'renta_id' => $rentaParams['mainData']['renta_id'],
                        'renta_value' => $rentaParams['mainData']['renta_value'],
                    ];
                }
            }

            $UserDbController->disableRentaMatViewTriggers();

            $rpcParams['contract_id'] = $annex_id;
            $rpcParams['is_annex'] = true;
            $rpcParams['plot_data_array'] = $rpcParams['added_plots_data'];
            $rpcParams['owner_results_by_plot'] = $owner_results_by_plot;
            $rpcParams['farming_results_by_plot'] = $farming_results_by_plot;
            $relations = $UserDbContractsController->addPlotToContractAnnex($rpcParams, $this->User, $this->module, $this->service_id, $excludeRemovedWithAnnex = false);

            $UsersController->groupLog($this->User->Name, $this->User->UserID, $this->User->GroupID, $this->module, $this->service_id, __METHOD__, $options['mainData'], $annex_id, 'Add annex');
            if (count($rpcParams['additionalRentas']) > 0) {
                $UsersController->groupLog($this->User->Name, $this->User->UserID, $this->User->GroupID, $this->module, $this->service_id, __METHOD__, $rents, $annex_id, 'Add annex rents');
            }
        } catch (Exception $e) {
            $UserDbController->deleteItemsByParams([
                'tablename' => $UserDbController->DbHandler->contractsRentsRelTable,
                'id_name' => 'contract_id',
                'id_string' => $annex_id,
            ]);

            $UserDbController->deleteItemsByParams([
                'tablename' => $UserDbController->DbHandler->contractsPlotsRelTable,
                'id_name' => 'contract_id',
                'id_string' => $annex_id,
            ]);

            $UserDbController->deleteItemsByParams([
                'tablename' => $UserDbController->DbHandler->tableContracts,
                'id_string' => $annex_id,
            ]);

            throw $e;
        } finally {
            $UserDbController->enableRentaMatViewTriggers();
        }

        // get contract_area for removed plots from original contract or annex
        $groupId = Prado::getApplication()->getUser()->GroupID;
        $options = [
            'return' => [
                'pc.plot_id', 'round(pc.contract_area::numeric, 3) as contract_area', 'round(pc.area_for_rent::numeric, 3) as area_for_rent',
                'kvs.ekate',
                'kvs.kad_ident',
                'max(file_id) as file_id',
                'max(date_uploaded) as date_uploaded',
            ],
            'joins' => [
                "LEFT JOIN dblink('host=" . DEFAULT_DB_HOST . ' port=' . DEFAULT_DB_PORT . ' user=' . DEFAULT_DB_USERNAME . ' password=' . DEFAULT_DB_PASSWORD . ' dbname=' . DEFAULT_DB_DATABASE . "',
                'SELECT id AS file_id, ekate AS ekatte_code, date_uploaded, group_id, shape_type FROM su_users_files') AS files (file_id integer, ekatte_code text, date_uploaded timestamp, group_id integer, shape_type integer) 
                ON kvs.ekate = files.ekatte_code AND files.group_id = {$groupId} AND files.shape_type = " . Config::LAYER_TYPE_KVS_OSZ,
            ],
            'group' => 'pc.plot_id, pc.contract_area, kvs.ekate, kvs.kad_ident, area_for_rent',
            'where' => [
                'contract_id' => ['column' => 'contract_id', 'compare' => '=', 'prefix' => 'pc', 'value' => ($rpcParams['annex_id']) ? $rpcParams['annex_id'] : $contract_id],
                'plot_id' => ['column' => 'plot_id', 'compare' => 'IN', 'prefix' => 'pc', 'value' => $rpcParams['removed_plots_gids']],
            ],
        ];
        $removedPlots = $UserDbContractsController->getContractPlotData($options, false, false);

        $plotsAreaByGid = [];
        $plotsAreaForRentByGid = [];
        $removedPlotsData = [];
        foreach ($removedPlots as $plot) {
            $plotId = $plot['plot_id'];
            $ekate = $plot['ekate'];

            $plotsAreaByGid[$plotId] = $plot['contract_area'];
            $plotsAreaForRentByGid[$plotId] = $plot['area_for_rent'];

            if ($plot['file_id']) {
                if (!isset($removedPlotsData[$ekate])) {
                    $removedPlotsData[$ekate] = ['plots_data' => []];
                }

                $removedPlotsData[$ekate]['file_id'] = $plot['file_id'];
                $removedPlotsData[$ekate]['plots_data'][] = [
                    'kad_ident' => $plot['kad_ident'],
                    'edit_active_from' => (DateTime::createFromFormat('Y-m-d H:i:s.u', $plot['date_uploaded']))->modify('-1 day')->format('Y-m-d'),
                ];
            }
        }

        $removedPlotsGidsCount = count($rpcParams['removed_plots_gids']);
        for ($i = 0; $i < $removedPlotsGidsCount; $i++) {
            $options = [
                'tablename' => $UserDbController->DbHandler->contractsPlotsRelTable,
                'mainData' => [
                    'contract_id' => $annex_id,
                    'plot_id' => $rpcParams['removed_plots_gids'][$i],
                    'contract_area' => $plotsAreaByGid[$rpcParams['removed_plots_gids'][$i]],
                    'area_for_rent' => $plotsAreaForRentByGid[$rpcParams['removed_plots_gids'][$i]],
                    'annex_action' => 'removed',
                    'contract_end_date' => $contractData['due_date'],
                ],
            ];

            $rel_id = $UserDbController->addItem($options);

            $UsersController->groupLog($this->User->Name, $this->User->UserID, $this->User->GroupID, $this->module, $this->service_id, __METHOD__, $options['mainData'], ['contract_plot_relation' => $rel_id], 'Remove plot from annex');

            // add owners data to removed plots
            if (array_key_exists($rpcParams['removed_plots_gids'][$i], $owner_results_by_plot)) {
                $owner_data = $owner_results_by_plot[$rpcParams['removed_plots_gids'][$i]];
                $owner_dataCount = count($owner_data);
                for ($j = 0; $j < $owner_dataCount; $j++) {
                    $options = [
                        'tablename' => $UserDbController->DbHandler->plotsOwnersRelTable,
                        'mainData' => $owner_data[$j],
                    ];

                    $options['mainData']['pc_rel_id'] = $rel_id;
                    unset($options['mainData']['plot_id'], $options['mainData']['id']);

                    $po_rel_id = $UserDbController->addItem($options);
                    $UsersController->groupLog($this->User->Name, $this->User->UserID, $this->User->GroupID, $this->module, $this->service_id, __METHOD__, array_merge($options['mainData'], ['plot_id' => $owner_data[$j]['plot_id']]), ['plot_owner_relation' => $po_rel_id], 'Add plot-owner relation of removed plot from annex');
                }
            }

            // add farming data to removed plots
            if (array_key_exists($rpcParams['removed_plots_gids'][$i], $farming_results_by_plot)) {
                $farming_data = $farming_results_by_plot[$rpcParams['removed_plots_gids'][$i]];
                $farming_dataCount = count($farming_data);
                for ($j = 0; $j < $farming_dataCount; $j++) {
                    $options = [
                        'tablename' => $UserDbController->DbHandler->plotsFarmingRelTable,
                        'mainData' => $farming_data[$j],
                    ];

                    $options['mainData']['pc_rel_id'] = $rel_id;
                    unset($options['mainData']['plot_id'], $options['mainData']['id']);

                    $pf_rel_id = $UserDbController->addItem($options);

                    $UsersController->groupLog($this->User->Name, $this->User->UserID, $this->User->GroupID, $this->module, $this->service_id, __METHOD__, array_merge($options['mainData'], ['plot_id' => $farming_data[$j]['plot_id']]), ['plot_farming_relation' => $pf_rel_id], 'Add plot-farming relation of removed plot from annex');
                }
            }
        }

        array_walk($removedPlotsData, function ($data, $ekate) {
            if ($data['file_id']) {
                $this->tryResolveContracts(
                    $data['file_id'],
                    $ekate,
                    $data['plots_data']
                );
            }
        });

        $UserDbContractsController->manageOverallRenta($annex_id);

        return $annex_id;
    }

    /**
     * Returns if the contract has historical plots.
     *
     * @api-method hasContractEditedPlots
     *
     * @param int $contract_id
     *
     * @return bool
     */
    public function hasContractEditedPlots($contract_id)
    {
        $UserDbContractsController = new UserDbContractsController($this->User->Database);
        $return = false;

        $options = [
            'return' => [
                'bool_or(kvs.is_edited)',
            ],
            'where' => [
                'contract_id' => ['column' => 'id', 'compare' => '=', 'prefix' => 'c', 'value' => $contract_id],
                'annex_action' => ['column' => 'annex_action', 'compare' => '=', 'prefix' => 'pc', 'value' => 'added'],
            ],
        ];

        $result = $UserDbContractsController->hasContractEditedPlots($options, false, false);

        if ('boolean' == gettype($result[0]['bool_or'])) {
            $return = $result[0]['bool_or'];
        }

        return $return;
    }

    /**
     * Приравняване на площите по договор към площ по документи на всеки имот
     * в случаите, в които въведената площ по договор се различава с до 10 кв.м.
     * площта по документ на имота. В случаите, в които няма площ по документ от КВС
     * се взема площ.
     *
     * @throws MTRpcException -33213 CONTRACT_PLOT_AREA_EQUALIZING_FAILED
     *
     * @return true
     */
    public function initAreaEqualize()
    {
        $UserDbController = new UserDbController($this->User->Database);
        $UsersController = new UsersController('Users');

        try {
            $UserDbController->equalizeContractAndPlotAreas();
            $UsersController->groupLog($this->User->Name, $this->User->UserID, $this->User->GroupID, $this->module, $this->service_id, __METHOD__, 'success', [], 'Contract plot area equalizing');

            return true;
        } catch (Exception $e) {
            $UsersController->groupLog($this->User->Name, $this->User->UserID, $this->User->GroupID, $this->module, $this->service_id, __METHOD__, ['error' => $e], 'fail', 'Contract plot area equalizing');

            throw new MTRpcException('CONTRACT_PLOT_AREA_EQUALIZING_FAILED', -33213);
        }
    }

    /**
     * Проверка дали договорите, които са филтрирани за мултиредакция за от тип "Аренда", "Наем" или "Съвместна обработка".
     *
     * @api-method getAllFilteredContracts
     *
     * @param array $filterObj contains all filter parameters
     *                         {
     *                         #item string c_num
     *                         #item array c_type
     *                         {
     *                         #item integer contract_types
     *                         }
     *                         #item integer c_status
     *                         #item array farming
     *                         {
     *                         #item integer farming_ids
     *                         }
     *                         #item integer farming_year
     *                         #item array renta_types
     *                         {
     *                         #item integer renta_types
     *                         }
     *                         #item timestamp date_from
     *                         #item timestamp date_to
     *                         #item timestamp due_date_from
     *                         #item timestamp due_date_to
     *                         #item bool   with_renta_nat
     *                         #item string kad_ident
     *                         #item array ekate
     *                         {
     *                         #item string EKATTE identifiers
     *                         }
     *                         #item string masiv
     *                         #item string number
     *                         #item array category
     *                         {
     *                         #item integer category ids
     *                         }
     *                         #item array ntp
     *                         {
     *                         #item integer NPT ids
     *                         }
     *                         #item string owner_name
     *                         #item string owner_egn
     *                         #item string rep_name
     *                         #item string rep_egn
     *                         #item string company_name
     *                         #item string company_eik
     *                         }
     *
     * @throws MTRpcException -33652 NO_CONTRACTS_FOR_MULTIEDIT
     *
     * @return array Array of relevant to user, sorted and filtered contracts
     */
    public function getAllFilteredContracts($obj)
    {
        // get all filtered contracts
        $obj['without_from_subleases'] = true;
        $filteredContracts = $this->getContractsTree($obj);

        if (0 == count($filteredContracts)) {
            throw new MTRpcException('NO_CONTRACTS_FOR_MULTIEDIT', -33652);
        }

        // check if the contracts are with correct contract type("Аренда", "Наем" или "Съвместна обработка")
        $correctContractTypes = $this->checkTypeOfContracts($filteredContracts);

        if ($correctContractTypes) {
            return $filteredContracts;
        }
    }

    /**
     * Мултиредакция на договори.
     *
     * @api-method saveMultiEditContracts
     *
     * @param array $contracts All filtered contracts
     * @param array $data Data from Multiedit form
     *
     * @throws MTRpcException -33652 NO_CONTRACTS_FOR_MULTIEDIT, -33653 NO_MULTIEDIT_VALUES
     */
    public function saveMultiEditContracts($contracts, $data)
    {
        if (true == $data['includeAnnexes']) {
            $contracts = array_merge($contracts, ...array_column($contracts, 'children'));
        }

        $UserDbController = new UserDbController($this->User->Database);
        $UserDbPaymentsController = new UserDbPaymentsController($this->User->Database);

        $renta = $data['renta'];
        $rentaNatura = $data['additionalRentas'];
        $setAreaForRentToAllowableArea = $data['setAreaForRentToAllowableArea'];
        $setAreaForRentToContractArea = $data['setAreaForRentToContractArea'];
        $setAreaForRentToArableArea = $data['setAreaForRentToArableArea'];

        $multiRentsCount = count($rentaNatura);

        if (0 == count($contracts)) {
            throw new MTRpcException('NO_CONTRACTS_FOR_MULTIEDIT', -33652);
        }
        if ('' == $renta && 0 == $multiRentsCount && false == $setAreaForRentToAllowableArea && false == $setAreaForRentToContractArea && false == $setAreaForRentToArableArea) {
            throw new MTRpcException('NO_MULTIEDIT_VALUES', -33653);
        }

        $hasNoEmptyRentInKind = false;
        $rentsInKindWithValue = array_filter($rentaNatura, function ($item) {
            return $item['value'] > 0;
        });

        if (count($rentsInKindWithValue) > 0) {
            $hasNoEmptyRentInKind = true;
        }

        if ($hasNoEmptyRentInKind || '' != $renta) {
            foreach ($contracts as $contract) {
                $UserDbPaymentsController->hasPaymentRestriction($contract['id']);
            }
        }

        $allContractsId = [];
        $contractsCount = count($contracts);

        for ($i = 0; $i < $contractsCount; $i++) {
            $contractID = $contracts[$i]['attributes']['id'];
            $allContractsId[] = $contractID;
        }

        $allContractsIdStr = implode(',', $allContractsId);

        // updating area_for_rent
        if (true == $setAreaForRentToAllowableArea) {
            $UserDbController->updateAreaForRentToAllowableArea($allContractsIdStr);
        }

        // updatign area for rent with contract area
        if (true == $setAreaForRentToContractArea) {
            $UserDbController->updateAreaForRentToContractArea($allContractsIdStr);
        }

        // updatign area for rent with contract area
        if (true == $setAreaForRentToArableArea) {
            $UserDbController->updateAreaForRentToArableArea($allContractsIdStr);
        }

        // Рента в лева
        if ('' != $renta) {
            $rentaOptions = [
                'tablename' => $UserDbController->DbHandler->tableContracts,
                'id_string' => $allContractsIdStr,
                'mainData' => [
                    'renta' => $renta,
                ],
            ];

            $UserDbController->editItem($rentaOptions);
        }

        // Рента в натура
        if ($multiRentsCount > 0) {
            for ($k = 0; $k < $multiRentsCount; $k++) {
                $rentaType = $rentaNatura[$k]['type'];
                $rentaValue = $rentaNatura[$k]['value'];

                if ('' == $rentaType || '0' == $rentaType) {
                    continue;
                }

                $naturaOptions = [
                    'tablename' => $UserDbController->DbHandler->contractsRentsRelTable,
                    'return' => ["STRING_AGG(contract_id::TEXT, ',') as contracts_id"],
                    'where' => [
                        'contract_id' => ['column' => 'contract_id', 'compare' => 'IN', 'value' => $allContractsId],
                        'renta_id' => ['column' => 'renta_id', 'compare' => '=', 'value' => $rentaType],
                    ],
                ];

                // Get all contracts with this renta type
                $contractsWithRentaType = $UserDbController->getItemsByParams($naturaOptions, false, false);
                $allContractsRentaType = $contractsWithRentaType[0]['contracts_id'];

                $allContractsRentaTypeArr = [];
                if ($allContractsRentaType) {
                    $allContractsRentaTypeArr = explode(',', $allContractsRentaType);

                    if ('-' == $rentaValue) {
                        $rentaNaturaDeleteOptions = [
                            'tablename' => $UserDbController->DbHandler->contractsRentsRelTable,
                            'id_name' => 'contract_id',
                            'id_string' => $allContractsRentaType,
                            'where' => [
                                'renta_id' => ['column' => 'renta_id', 'compare' => '=', 'value' => $rentaType],
                            ],
                        ];
                        $UserDbController->deleteItemsByParams($rentaNaturaDeleteOptions);

                        continue;
                    }

                    $rentaNaturaOptions = [
                        'tablename' => $UserDbController->DbHandler->contractsRentsRelTable,
                        'id_name' => 'contract_id',
                        'id_string' => $allContractsRentaType,
                        'mainData' => [
                            'renta_value' => $rentaValue,
                        ],
                        'where' => [
                            'renta_id' => $rentaType,
                        ],
                    ];

                    $UserDbController->editItem($rentaNaturaOptions);
                }

                if ('-' == $rentaValue) {
                    continue;
                }

                $allContractsWithOutRentaType = array_diff($allContractsId, $allContractsRentaTypeArr);
                if (count($allContractsWithOutRentaType) > 0) {
                    $allContractsValues = [];
                    foreach ($allContractsWithOutRentaType as $key => $value) {
                        $allContractsValues[] = ['contract_id' => (int)$value,
                            'renta_id' => (int)$rentaType,
                            'renta_value' => (float)$rentaValue];
                    }

                    $addRentaNaturaOptions = [
                        'tablename' => $UserDbController->DbHandler->contractsRentsRelTable,
                        'columns' => 'contract_id, renta_id, renta_value',
                        'values' => $allContractsValues,
                    ];

                    $UserDbController->addItems($addRentaNaturaOptions);
                }
            }
        }
    }

    public function checkForLongerThanYear($rpcParams)
    {
        $UserDbController = new UserDbController($this->User->Database);

        $c_types_array = [
            Config::CONTRACT_TYPE_LEASE,
            Config::CONTRACT_TYPE_RENT,
            Config::CONTRACT_TYPE_JOINT_PROCESSING,
        ];
        $options = [
            'tablename' => $UserDbController->DbHandler->tableContracts,
            'return' => [
                'start_date as contract_start_date',
                'due_date as contract_due_date',
            ],
            'where' => [
                'ids' => ['column' => 'id', 'compare' => 'IN', 'value' => $rpcParams['contract_ids']],
                'nm_usage_rights' => ['column' => 'nm_usage_rights', 'compare' => 'IN', 'value' => $c_types_array],
                'is_sublease' => ['column' => 'is_sublease', 'compare' => '=', 'value' => 'FALSE'],
                'is_annex' => ['column' => 'is_annex', 'compare' => '=', 'value' => 'FALSE'],
                'active' => ['column' => 'active', 'compare' => '=', 'value' => 'TRUE'],
            ],
        ];

        $contracts = $UserDbController->getItemsByParams($options);

        foreach ($contracts as $contract) {
            $start_year = date('Y', strtotime($contract['contract_start_date']));
            $due_year = date('Y', strtotime($contract['contract_due_date']));

            $period = $due_year - $start_year;
            $longerThanYear = $period > 1;
            if ($longerThanYear) {
                return true;
            }
        }

        return false;
    }

    /**
     * @throws MTRpcException
     *
     * @return bool|mixed
     */
    public function multiCopyContracts($rpcParams)
    {
        $UserDbController = new UserDbController($this->User->Database);
        $UserDbContractsController = new UserDbContractsController($this->User->Database);
        $UserDbController->disableRentaMatViewTriggers();

        $rpcParams['c_types_array'] = [
            Config::CONTRACT_TYPE_LEASE,
            Config::CONTRACT_TYPE_RENT,
            Config::CONTRACT_TYPE_JOINT_PROCESSING,
        ];

        $contracts = $this->getContractsToCopy($rpcParams);

        if (0 == count($contracts)) {
            throw new MTRpcException('NO_CONTRACTS_FOR_MULTICOPY', -33654);
        }
        $longerThanOneYear = false;

        $contractWithDuplicatedPlots = [];

        foreach ($contracts as $contract) {
            $tmpContract = $this->replaceContractDates($contract);

            if (!$longerThanOneYear) {
                $longerThanOneYear = $tmpContract['longerThanOneYear'];
            }

            $tmpContract = $tmpContract['contract'];
            $rentaOptions = [
                'return' => [
                    'renta_id as type', 'renta_value as value',
                ],
                'where' => [
                    'id' => ['column' => 'contract_id', 'compare' => '=', 'value' => $contract['id']],
                ],
            ];
            $rentaResult = $UserDbContractsController->getContractRentsData($rentaOptions);
            $rentaResultCount = count($rentaResult);
            if ($rentaResultCount > 0) {
                for ($i = 0; $i < $rentaResultCount; $i++) {
                    $tmpContract['additionalRentas'][$i] = $rentaResult[$i];
                }
            }
            if (true == $rpcParams['copy_contract_files']) {
                $tmpContract['copy_contract_files'] = true;
            } else {
                $tmpContract['copy_contract_files'] = false;
            }

            $tmpContract['copy_id'] = $tmpContract['id'];
            $tmpContract['record_id'] = 0;
            unset($tmpContract['id']);

            try {
                $this->addEditContract($tmpContract);
            } catch (MTRpcException $exception) {
                if (UserDbContractsController::CONTRACT_PLOT_DUPLICATION_ERROR_CODE == $exception->getErrorCodeNumber()) {
                    $contractsInfo = $exception->getCustomErrorMessage();
                    foreach ($contractsInfo as $cid => $info) {
                        if (array_key_exists($cid, $contractWithDuplicatedPlots)) {
                            $contractWithDuplicatedPlots[$cid]['gids'] = array_merge($contractWithDuplicatedPlots[$cid]['gids'], $info['gids']);
                            $contractWithDuplicatedPlots[$cid]['plot_kad_idents'] .= ',' . $info['plot_kad_idents'];
                        } else {
                            $contractWithDuplicatedPlots[$cid] = $info;
                        }
                    }
                }
            }
        }

        $UserDbController->enableRentaMatViewTriggers();
        $UserDbController->refreshRentaViews();

        if (count($contractWithDuplicatedPlots) > 0) {
            // used just to notify customer about not successfully copied contracts
            throw new MTRpcException($contractWithDuplicatedPlots, UserDbContractsController::CONTRACT_PLOT_DUPLICATION_ERROR_CODE);
        }

        return $longerThanOneYear;
    }

    public function initAnnexRenta(int $contractId): array
    {
        $UserDbContractsController = new UserDbContractsController($this->User->Database);

        $contract = array_pop($UserDbContractsController->getContractsToProcess(['contract_ids' => [$contractId]]));

        $rentaOptions = [
            'where' => [
                'id' => ['column' => 'contract_id', 'compare' => '=', 'value' => $contract['id']],
            ],
        ];
        $rentaResult = $UserDbContractsController->getContractRentsData($rentaOptions);
        $rentaResultCount = count($rentaResult);
        if ($rentaResultCount > 0) {
            for ($i = 0; $i < $rentaResultCount; $i++) {
                $contract['additionalRentas'][$i] = $rentaResult[$i];
            }
        }

        return $contract;
    }

    public function exportFilteredContractsToXLS($rpcParams)
    {
        $result = $this->getContractsTree($rpcParams);
        $body = array_column($result, 'attributes');

        $headers = [
            'nm_usage_rights' => 'Tип на договор',
            'c_num' => '№ на договор',
            'c_date' => 'Дата на договор',
            'start_date' => 'Влиза в сила от',
            'due_date' => 'Крайна дата',
            'osz_num' => 'ОСЗ №',
        ];

        $excellExportOptions = [
            'noStyle' => true,
            'format' => [
                'nm_usage_rights' => [
                    'value' => PHPExcel_Style_NumberFormat::FORMAT_TEXT,
                ],
                'c_num' => [
                    'type' => PHPExcel_Style_NumberFormat::FORMAT_TEXT,
                ],
                'c_date' => [
                    'type' => PHPExcel_Style_NumberFormat::FORMAT_TEXT,
                ],
                'start_date' => [
                    'type' => PHPExcel_Style_NumberFormat::FORMAT_TEXT,
                ],
                'due_date' => [
                    'type' => PHPExcel_Style_NumberFormat::FORMAT_TEXT,
                ],
                'osz_num' => [
                    'type' => PHPExcel_Style_NumberFormat::FORMAT_TEXT,
                ],
            ],
        ];

        $name = 'filtred_contract_exportD73.xlsx';
        $path = PUBLIC_UPLOAD_EXPORT . '/' . $this->User->GroupID . '/';

        if (!is_dir($path)) {
            mkdir($path);
        }

        $exportExcel = new ExportToExcelClass();
        $exportExcel->export($body, $headers, [], 0, $excellExportOptions);
        $exportExcel->saveFile($path . $name);

        return [
            'file_path' => PUBLIC_UPLOAD_EXPORT_RELATIVE_PATH . $this->User->GroupID . '/' . $name,
            'file_name' => $name,
        ];
    }

    protected function formatDate($string, $to_format = 'd.m.Y')
    {
        $from_format = 'Y-m-d H:i:s';
        $return = DateTime::createFromFormat($from_format, $string);

        if ($return instanceof DateTime) {
            return $return->format($to_format);
        }

        return $string;
    }

    private function formatTree($results, $finalFarming, $rentaTypes)
    {
        $formatted = [
            'rows' => [],
            'full_count' => null,
        ];

        foreach ($results as $node) {
            $formatted['rows'][$node['id']] = $this->formatContractData($node, $finalFarming, $rentaTypes);

            if (!empty($node['children'])) {
                $children = json_decode($node['children'], true);
                $formattedChildren = [];
                foreach ($children as $child) {
                    $formattedChildren[] = $this->formatContractData($child, $finalFarming, $rentaTypes);
                }

                $formatted['rows'][$node['id']]['children'] = $formattedChildren;
            }

            if (empty($formatted['full_count']) && !empty($node['full_count'])) {
                $formatted['full_count'] = $node['full_count'];
            }
        }

        $formatted['rows'] = array_values($formatted['rows']);

        return $formatted;
    }

    private function buildTree(array $flatList, $finalFarming, $rentaTypes)
    {
        $grouped = [];
        foreach ($flatList as $node) {
            if (0 == $node['parent_id']) {
                $children = [];
                if (array_key_exists('children', $grouped[$node['id']])) {
                    $children = $grouped[$node['id']]['children'];
                }
                $grouped[$node['id']] = $this->formatContractData($node, $finalFarming, $rentaTypes);
                $grouped[$node['id']]['children'] = $children;
            } else {
                $grouped[$node['parent_id']]['children'][] = $this->formatContractData($node, $finalFarming, $rentaTypes);
            }
        }

        return array_values($grouped);
    }

    private function formatContractData(&$contractData, $finalFarming, $rentaTypes)
    {
        date_default_timezone_set('Europe/Sofia');
        $currentDate = date('Y-m-d', time());
        $UserDbController = new UserDbController($this->User->Database);

        $contractData['c_date'] = $this->formatDate($contractData['c_date']);
        $contractData['start_date'] = $this->formatDate($contractData['start_date']);
        $contractData['start_date_db_format'] = date('Y-m-d', strtotime($contractData['start_date']));

        if ($contractData['active']) {
            $contractData['active_text'] = (!$contractData['due_date'] || $contractData['due_date'] >= $currentDate) ? 'Действащ' : 'Изтекъл';
        } else {
            $contractData['active_text'] = 'Анулиран';
        }

        if (!$contractData['comment']) {
            $contractData['comment'] = '-';
        }

        if (!$contractData['sv_num']) {
            $contractData['sv_num'] = '-';
        }

        if ('' == $contractData['sv_date']) {
            $contractData['sv_date'] = '-';
        } else {
            $contractData['sv_date'] = $this->formatDate($contractData['sv_date']);
        }

        if (!$contractData['osz_num'] || '' == $contractData['osz_num']) {
            $contractData['osz_num'] = '-';
        }

        if ('' == $contractData['osz_date']) {
            $contractData['osz_date'] = '-';
        } else {
            $contractData['osz_date'] = $this->formatDate($contractData['osz_date']);
        }

        if (!$contractData['sv_num']) {
            $contractData['sv_num'] = '-';
        }

        if ('' == $contractData['payday']) {
            $contractData['payday'] = '-';
            $contractData['paymonth'] = '';
        } else {
            $payday = explode('-', $contractData['payday']);
            $contractData['payday'] = $payday[0];
            $contractData['paymonth'] = $payday[1];
        }

        if ('' != $contractData['due_date']) {
            $contractData['due_date_db_format'] = date('Y-m-d', strtotime($contractData['due_date']));
            $contractData['due_date'] = $this->formatDate($contractData['due_date']);
            $text = $contractData['c_num'] . ' (' . $contractData['start_date'] . ' - ' . $contractData['due_date'] . ')';
        } else {
            $contractData['due_date'] = '-';
            $text = $contractData['c_num'] . ' (' . $contractData['start_date'] . ')';
        }

        if (!$contractData['na_num']) {
            $contractData['na_num'] = '-';
        }

        if (!$contractData['tom']) {
            $contractData['tom'] = '-';
        }

        if (!$contractData['delo']) {
            $contractData['delo'] = '-';
        }

        if (!$contractData['court']) {
            $contractData['court'] = '-';
        }

        if (!$contractData['renta']) {
            $contractData['renta_text'] = '-';
        } else {
            $contractData['renta'] = number_format($contractData['renta'], 2, '.', '');
            $contractData['renta_text'] = BGNtoEURO($contractData['renta']);
        }

        if ($contractData['overall_renta']) {
            $contractData['overall_renta'] = BGNtoEURO($contractData['overall_renta']);
        }

        $contractData['is_closed_for_editing_text'] = $contractData['is_closed_for_editing'] ? 'Да' : 'Не';
        $contractData['is_declaration_subleased_text'] = $contractData['is_declaration_subleased'] ? 'Да' : 'Не';

        $contractData['c_type'] = $contractData['nm_usage_rights'];
        $contractData['nm_usage_rights'] = $GLOBALS['Contracts']['ContractTypes'][$contractData['nm_usage_rights']]['name'];
        $contractData['farming'] = $finalFarming[$contractData['farming_id']]['name'];

        $rentaOptions = [
            'tablename' => $UserDbController->DbHandler->contractsRentsRelTable,
            'where' => [
                'id' => ['column' => 'contract_id', 'compare' => '=', 'value' => $contractData['id']],
            ],
        ];

        $tmpRenta = $UserDbController->getItemsByParams($rentaOptions);
        $tmpRentCount = count($tmpRenta);
        for ($j = 0; $j < $tmpRentCount; $j++) {
            $tmpRenta[$j]['renta_nat_type'] = $rentaTypes[$tmpRenta[$j]['renta_id']]['name'];
            $tmpRenta[$j]['renta_nat_text'] = $tmpRenta[$j]['renta_value'] . ' ' . $rentaTypes[$tmpRenta[$j]['renta_id']]['unit'];
        }

        // check for payment
        $contractData['hasPayment'] = $contractData['payment'] > 0;

        $contractData['additionalRentas'] = $tmpRenta;

        return [
            'id' => $contractData['id'],
            'text' => $text,
            'attributes' => $contractData,
            'iconCls' => $this->getContractIcon($contractData),
        ];
    }

    private function getContractIcon($contractInfo)
    {
        $contractType = (int) $contractInfo['c_type'];
        switch (true) {
            case (true == $contractInfo['is_annex']):
                $ret = Config::$contractTypeIconsMap[Config::ICON_CONTRACT_DEFAULT];

                break;
            case (is_int($contractInfo['from_sublease'])):
                $ret = Config::$contractTypeIconsMap[Config::ICON_CONTRACT_TYPE_FROM_SUBLEASE];

                break;
            case (Config::CONTRACT_TYPE_OWN === $contractType):
                $ret = Config::$contractTypeIconsMap[Config::ICON_CONTRACT_TYPE_OWN];

                break;
            case (Config::CONTRACT_TYPE_LEASE === $contractType):
                $ret = Config::$contractTypeIconsMap[Config::ICON_CONTRACT_TYPE_LEASE];

                break;
            case (Config::CONTRACT_TYPE_RENT === $contractType):
                $ret = Config::$contractTypeIconsMap[Config::ICON_CONTRACT_TYPE_RENT];

                break;
            default:
                $ret = Config::$contractTypeIconsMap[Config::ICON_CONTRACT_DEFAULT];
        }

        return $ret;
    }

    private function getHeritorsOwners($tmpOwnerIdOption)
    {
        $UserDbController = new UserDbController($this->User->Database);
        $UserDbOwnersController = new UserDbOwnersController($this->User->Database);

        $currentOwnerIds = $UserDbController->getItemsByParams($tmpOwnerIdOption, false, false);
        foreach ($currentOwnerIds as $currentOwnerId) {
            $ownerIds[] = $currentOwnerId['id'];
        }

        if (count($ownerIds) > 0) {
            $tmpOwnerOptions = [
                'return' => [
                    'pc_rel_id',
                ],
                'tablename' => $UserDbOwnersController->DbHandler->plotsOwnersRelTable,
                'where' => [
                    'owner_id' => ['column' => 'owner_id', 'compare' => 'IN', 'value' => $ownerIds],
                    'is_heritor' => ['column' => 'is_heritor', 'compare' => '=', 'value' => 'true'],
                ],
            ];

            $tmpParentPath = $UserDbOwnersController->getItemsByParams($tmpOwnerOptions, false, false);

            if (count($tmpParentPath) > 0) {
                foreach ($tmpParentPath as $ownerId) {
                    $returnIds[] = $ownerId['pc_rel_id'];
                }

                return array_values(array_unique($returnIds));
            }
        }
    }

    private function validateAnnexDates($rpcParams, $contractData)
    {
        if (true != $contractData['is_annex']) {
            return;
        }

        $UserDbController = new UserDbController($this->User->Database);

        $options = [
            'tablename' => $UserDbController->DbHandler->tableContracts,
            'where' => [
                'parent_id' => ['column' => 'parent_id', 'compare' => '=', 'value' => $contractData['parent_id']],
                'annex_id' => ['column' => 'id', 'compare' => '<>', 'value' => $rpcParams['record_id']],
                'active' => ['column' => 'active', 'compare' => '=', 'value' => 'true'],
            ],
        ];

        $results = $UserDbController->getItemsByParams($options);

        $resultsCount = count($results);
        for ($i = 0; $i < $resultsCount; $i++) {
            if (!(date('Y-m-d', strtotime($results[$i]['due_date'])) < date('Y-m-d', strtotime($rpcParams['contract_start_date']))
                    || date('Y-m-d', strtotime($results[$i]['start_date'])) > date('Y-m-d', strtotime($rpcParams['contract_due_date'])))) {
                throw new MTRpcException('ANNEXES_INVALID_ANNEX_DATE', -33202);
            }
        }
    }

    private function copyContractRelData($fromContractID, $toContractID, $toContractDueDate = null, $toContractType = null, $copyContractFiles = false)
    {
        $UserDbController = new UserDbController($this->User->Database);
        $UserDbContractsController = new UserDbContractsController($this->User->Database);
        $UsersController = new UsersController('Users');

        // get contract plot rel data
        $results = $UserDbContractsController->getContractsPlotsRelations($fromContractID);

        $resultsCount = count($results);
        for ($i = 0; $i < $resultsCount; $i++) {
            if (Config::ANNEX_ACTION_REMOVED == $results[$i]['annex_action']) {
                // do not copy removed plots
                continue;
            }

            $options = [
                'tablename' => $UserDbController->DbHandler->contractsPlotsRelTable,
                'mainData' => [
                    'contract_id' => $toContractID,
                    'plot_id' => $results[$i]['plot_id'],
                    'contract_area' => $results[$i]['contract_area'],
                    'area_for_rent' => $results[$i]['area_for_rent'],
                    'kvs_allowable_area' => $results[$i]['kvs_allowable_area'],
                    'price_per_acre' => $results[$i]['price_per_acre'],
                    'price_sum' => $results[$i]['price_sum'],
                    'contract_end_date' => 1 != $toContractType ? $toContractDueDate : null,
                    'rent_per_plot' => $results[$i]['rent_per_plot'] ? $results[$i]['rent_per_plot'] : null,
                    'comment' => $results[$i]['comment'],
                    'annex_action' => $results[$i]['annex_action'],
                ],
            ];

            $rel_id = $UserDbController->addItem($options);
            $UsersController->groupLog($this->User->Name, $this->User->UserID, $this->User->GroupID, $this->module, $this->service_id, __METHOD__, $options['mainData'], $rel_id, 'add copy contract-plot relation');

            $options = [
                'tablename' => $UserDbController->DbHandler->plotsOwnersRelTable,
                'where' => [
                    'pc_rel_id' => ['column' => 'pc_rel_id', 'compare' => '=', 'value' => $results[$i]['id']],
                ],
            ];

            $owner_results = $UserDbController->getItemsByParams($options);
            $owner_resultsCount = count($owner_results);
            for ($j = 0; $j < $owner_resultsCount; $j++) {
                $options = [
                    'tablename' => $UserDbController->DbHandler->plotsOwnersRelTable,
                    'mainData' => [
                        'pc_rel_id' => $rel_id,
                        'owner_id' => $owner_results[$j]['owner_id'],
                        'percent' => $owner_results[$j]['percent'],
                        'owner_document_id' => $owner_results[$j]['owner_document_id'],
                        'rep_id' => $owner_results[$j]['rep_id'],
                        'proxy_num' => $owner_results[$j]['proxy_num'],
                        'proxy_date' => $owner_results[$j]['proxy_date'],
                        'is_heritor' => $owner_results[$j]['is_heritor'] ? 'TRUE' : 'FALSE',
                        'path' => $owner_results[$j]['path'],
                        'is_set_manual' => $owner_results[$j]['is_set_manual'],
                    ],
                ];

                $po_rel_id = $UserDbController->addItem($options);
                $UsersController->groupLog($this->User->Name, $this->User->UserID, $this->User->GroupID, $this->module, $this->service_id, __METHOD__, $options['mainData'], $po_rel_id, 'add copy contract plot-owner relation');
            }

            $options = [
                'tablename' => $UserDbController->DbHandler->plotsFarmingRelTable,
                'where' => [
                    'pc_rel_id' => ['column' => 'pc_rel_id', 'compare' => '=', 'value' => $results[$i]['id']],
                ],
            ];

            $farming_results = $UserDbController->getItemsByParams($options);
            $farmingCount = count($farming_results);
            for ($j = 0; $j < $farmingCount; $j++) {
                $options = [
                    'tablename' => $UserDbController->DbHandler->plotsFarmingRelTable,
                    'mainData' => [
                        'pc_rel_id' => $rel_id,
                        'farming_id' => $farming_results[$j]['farming_id'],
                        'percent' => $farming_results[$j]['percent'],
                        'rep_id' => $farming_results[$j]['rep_id'],
                        'proxy_num' => $farming_results[$j]['proxy_num'],
                        'proxy_date' => $farming_results[$j]['proxy_date'],
                    ],
                ];

                $pf_rel_id = $UserDbController->addItem($options);
                $UsersController->groupLog($this->User->Name, $this->User->UserID, $this->User->GroupID, $this->module, $this->service_id, __METHOD__, $options['mainData'], $pf_rel_id, 'add copy contract plot-farming relation');
            }
        }

        $UserDbContractsController->manageOverallRenta($toContractID);
        if (true == $copyContractFiles) {
            $UserDbContractsController->copyContractFileRelations($fromContractID, $toContractID);
        }
    }

    /**
     * Check if the contracts are with correct contract type("Аренда", "Наем" или "Съвместна обработка").
     *
     * @param array $contracts all filtered contracts
     *
     * @throws MTRpcException -33651 INCORRECT_CONTRACTS_TYPE
     *
     * @return bool
     */
    private function checkTypeOfContracts($contracts)
    {
        $UserDbContractsController = new UserDbContractsController($this->User->Database);

        // "Аренда", "Наем" и "Съвместна обработка" ids
        $possibleContractTypes = [$GLOBALS['Contracts']['ContractTypes'][2]['id'],
            $GLOBALS['Contracts']['ContractTypes'][3]['id'],
            $GLOBALS['Contracts']['ContractTypes'][5]['id']];

        $contractsCount = count($contracts);
        for ($i = 0; $i < $contractsCount; $i++) {
            $contract = $contracts[$i];
            $contractType = $contract['attributes']['c_type'];

            $isFromSublease = $UserDbContractsController->isContractFromSublease($contract['id']);
            if ($isFromSublease) {
                throw new MTRpcException('CANNOT_MODIFY_FROM_SUBLEASE_CONTRACT', -33752);
            }
            if (!in_array($contractType, $possibleContractTypes)) {
                throw new MTRpcException('INCORRECT_CONTRACTS_TYPE', -33651);
            }
        }

        return true;
    }

    private function replaceContractDates($contract)
    {
        $start_year = date('Y', strtotime($contract['contract_start_date']));
        $start_date = date('Y-m-d', strtotime($start_year . '-10-01'));
        $due_year = date('Y', strtotime($contract['contract_due_date']));
        $due_date = date('Y-m-d', strtotime($due_year . '-09-30'));

        $period = $due_year - $start_year;

        $longerThanYear = $period > 1;

        if (Config::CONTRACT_TYPE_OWN != $contract['nm_usage_rights']) {
            if ($period < 1) {
                $period = strtotime($due_date) - strtotime($start_date);
                $period = floor($period / (60 * 60 * 24)) + 1;
                $contract['contract_start_date'] = date('Y-m-d', strtotime($start_date . '+' . $period . ' days'));
                $contract['contract_due_date'] = date('Y-m-d', strtotime($due_date . '+' . $period . ' days'));
            } else {
                $contract['contract_start_date'] = date('Y-m-d', strtotime($start_date . '+' . $period . ' years'));
                $contract['contract_due_date'] = date('Y-m-d', strtotime($due_date . '+' . $period . ' years'));
            }
            $contract['contract_date'] = date('Y-m-d');
        }

        return ['contract' => $contract, 'longerThanOneYear' => $longerThanYear];
    }

    private function getContractsToCopy(array $params): array
    {
        $UserDbContractsController = new UserDbContractsController($this->User->Database);

        return $UserDbContractsController->getContractsToProcess($params);
    }
}
