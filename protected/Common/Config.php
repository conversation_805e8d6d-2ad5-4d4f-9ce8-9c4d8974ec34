<?php

namespace TF\Application\Common;

/**
 * Config class file.
 *
 * <AUTHOR>
 */
/**
 * Common constant used in projects.
 */
class Config
{
    public const STATE_OFF = 'Off';
    public const STATE_DEBUG = 'Debug';
    public const STATE_NORMAL = 'Normal';
    public const STATE_PERFORMANCE = 'Performance';

    // User authorization module defined in application.xml
    public const AUTH_MODULE = 'auth';

    // Konfiguraciq na flagovete v saita.
    public const FLAGS_TRASH = 1;
    public const FLAGS_VISIBILITY = 2;

    /**
     * Expiration period for user's auto login COOKIE in seconds.
     * Default value is 2678400 (31 days).
     */
    public const COOKIE_EXPIRE_TIME = 2678400;

    // Default language
    public const DEFAULT_LANG = 'bg';

    // Important: This is closely related to $GLOBALS['Users']['account_types'] (/engine/Plugins/Core/Users/<USER>
    public const USERS_SUPER_ADMIN_FLAG = 1;
    public const USERS_ADMIN_FLAG = 2;
    public const USERS_NORMAL = 3;
    public const USERS_OPERATOR_FLAG = 4;
    public const USERS_SUPPORT_FLAG = 5;
    public const USERS_SALES_FLAG = 6;

    public const PAYMENT_TYPE_BANK = 0;
    public const PAYMENT_TYPE_POST = 1;

    // IMPORTANT: These keys are part of their own respective tables in the DB!
    public const SUBSCRIPTION_MAP = 'map';
    public const SUBSCRIPTION_PLOTS = 'plots';
    public const SUBSCRIPTION_AGRO = 'agro';

    public const REPORT_NATURA = 1;
    public const REPORT_GASKI = 2;
    public const REPORT_PARKOVE = 3;
    public const REPORT_REZERVATI = 4;
    public const REPORT_PTICI = 5;

    public const DEFAULT_MAX_EXTENT = '125190.6162 4573142.7188 631370.3273 4887149.5823';
    public const LAYER_BOUNDARY_DEFAULT_BORDER_WIDTH = 1;
    public const LAYER_COLOR_DEFAULT_TRANSPARENCY = 0;
    public const LAYER_BOUNDARY_DEFAULT_COLOR = '#000000';
    public const LAYER_LABEL_DEFAULT_SIZE = 8;

    public const LAYER_TYPE_ZP = 1;
    public const LAYER_TYPE_GPS = 2;
    public const LAYER_TYPE_DSS = 3;
    public const LAYER_TYPE_KMS = 4;
    public const LAYER_TYPE_KVS = 5;
    public const LAYER_TYPE_ISAK = 6;
    public const LAYER_TYPE_SATELLITE_WORK = 8;
    public const LAYER_TYPE_FOR_ISAK = 9;
    public const LAYER_TYPE_LFA = 10;
    public const LAYER_TYPE_NATURA_2000 = 11;
    public const LAYER_TYPE_PERMANETELY_GREEN_AREAS = 12;
    public const LAYER_TYPE_VPS_PASISHTA = 13;
    public const LAYER_TYPE_VPS_GASKI_CHERVENOGUSHI = 14;
    public const LAYER_TYPE_VPS_GASKI_ZIMNI = 15;
    public const LAYER_TYPE_VPS_LIVADEN_BLATAR = 16;
    public const LAYER_TYPE_VPS_ORLI_LESHOYADI = 17;
    public const LAYER_TYPE_KVS_OSZ = 18;
    public const LAYER_TYPE_WORK_LAYER = 19;
    public const LAYER_TYPE_EXCEL_IMPORT = 23;
    public const LAYER_TYPE_CADASTRE = 24;
    public const LAYER_TYPE_SLOPE = 25;
    public const LAYER_TYPE_TERRAIN = 26;
    public const LAYER_TYPE_ORTHOPHOTO = 27;
    public const LAYER_TYPE_CSD = 28;

    public const LAYER_TYPE_ALLOWABLE_DRAFT = 'layer_allowable_draft';
    public const LAYER_TYPE_ALLOWABLE = 'layer_allowable';
    public const LAYER_TYPE_ALLOWABLE_FINAL = 'layer_allowable_final';
    public const LAYER_TYPE_PHYSICAL_BLOCKS = 'layer_allowable';
    public const LAYER_TYPE_PHYSICAL_BLOCKS_CODE = 36;
    public const LAYER_TYPE_ALLOWABLE_FINAL_CODE = 36;

    public const LAYER_TYPE_PHYSICAL_BLOCKS_PRELIMINARY = 29;
    public const LAYER_TYPE_PERMANENT_GRASSLAND_FOR_MOWING_PRELIMINARY = 30;
    public const LAYER_TYPE_LANDSCAPE_ELEMENTS_POINTS = 31;
    public const LAYER_TYPE_LANDSCAPE_ELEMENTS_LINES = 32;
    public const LAYER_TYPE_LANDSCAPE_ELEMENTS_POLYGONS = 33;
    public const LAYER_TYPE_DS_PRC = 34;
    public const LAYER_TYPE_PERMANENT_GRASSLAND_FOR_MOWING = 35;
    // added to fix the issue with "Физически блокове 2023 г. - окончателен" layer name from TF\Engine\APIClasses\Diary\MapLayersTree.

    public const LAYER_COLUMN_CATEGORY_GID = 'gid';
    public const LAYER_COLUMN_CATEGORY_NAME = 'name';
    public const LAYER_COLUMN_CATEGORY_GEOM = 'geom';
    public const LAYER_COLUMN_CATEGORY_CROP = 'crop';
    public const LAYER_COLUMN_CATEGORY_CONTRACT = 'contract';
    public const LAYER_COLUMN_CATEGORY_LEGAL_RIGHTS = 'legal rights';
    public const LAYER_COLUMN_CATEGORY_DECLARED_AREA_STATUS = 'declared area status';
    public const LAYER_COLUMN_CATEGORY_EKATTE = 'ekatte';
    public const LAYER_COLUMN_CATEGORY_NTP = 'ntp';
    public const LAYER_COLUMN_CATEGORY_SLOPE = 'slope';
    public const LAYER_COLUMN_CATEGORY_TEXT = 'text';
    public const LAYER_COLUMN_CATEGORY_NUMBER = 'number';
    public const LAYER_COLUMN_CATEGORY_BOOLEAN = 'boolean';
    public const LAYER_COLUMN_CATEGORY_DATE = 'date';
    public const LAYER_COLUMN_CATEGORY_COLOR = 'color';
    public const LAYER_COLUMN_CATEGORY_LABEL = 'label';
    public const LAYER_COLUMN_CATEGORY_CATEGORY = 'category';

    public const LAYER_COLUMN_SELECTION_TYPE_MULTIPLE = 'multiple';
    public const LAYER_COLUMN_SELECTION_TYPE_SINGLE = 'single';

    public const ALTITUDE_MAP_START_COLOR = 0xA64B00;
    public const ALTITUDE_MAP_VALUE_RANGE = 5;

    public const OPERATION_CUT = 0;
    public const OPERATION_SPLIT = 1;
    public const OPERATION_DELETE = 2;

    public const TOOL_OPERATION_EDIT_GEOMETRY = 1;
    public const TOOL_OPERATION_DRAW_HOLE = 2;
    public const TOOL_OPERATION_MERGE = 3;
    public const TOOL_OPERATION_REMOVE_HOLES = 4;
    public const TOOL_OPERATION_SPLIT = 5;
    public const TOOL_OPERATION_SAVE = 6;
    public const TOOL_OPERATION_DRAW = 7;
    public const TOOL_OPERATION_SAVE_WITH_INFO = 8;
    public const TOOL_OPERATION_INTERSECT = 9;

    // Report ZDP
    public const MIN_TREATMENT_AREA = 30;
    public const LOW_TREATMENT_AREA_DIVER = 10;
    public const LOW_TREATMENT_AREA_ENP = 15;
    public const MIN_PERCENT_ENP = 5;
    public const MAX_PERCENT_FIRST_CROP = 75;
    public const MAX_PERCENT_FIRST_PLUS_SECOND_CROP = 95;

    // subsidies schemas
    public const SEPP = 1;
    public const ZDP = 2;
    public const PNDP = 3;
    public const NR = 4;
    public const NATURA = 5;
    public const VPS = 6;
    public const NR1 = 41;
    public const NR2 = 42;

    // Cron scripts Types
    public const CRON_TMP_PROCESSING = 1;

    // Device Types
    public const DEVICE_UNKNOWN = 0;
    public const DEVICE_TOPCON = 1;
    public const DEVICE_TRIMBLE = 2;
    public const DEVICE_OSZ = 3;
    public const DEVICE_MUELLER = 4;

    // User Rights
    public const CONTRACTS_READ_RIGHTS = 7;
    public const CONTRACTS_WRITE_RIGHTS = 8;
    public const CONTRACTS_OWN_WRITE_RIGHTS = 9;
    public const EQUITY_RIGHTS = 10;

    public const MAP_RIGHTS_R = 1;
    public const MAP_RIGHTS_RW = 12;

    public const PLOT_RIGHTS_R = 2;
    public const PLOT_RIGHTS_RW = 14;

    public const SUBSIDY_RIGHTS = 3;
    public const SUBSIDY_RIGHTS_RW = 15;

    public const AGRO_RIGHTS = 4;
    public const AGRO_RIGHTS_RW = 16;

    public const SALES_CONTRACTS_RIGHTS_R = 18;
    public const SALES_CONTRACTS_RIGHTS_RW = 19;
    public const HYPOTHECS_RIGHTS_R = 20;
    public const HYPOTHECS_RIGHTS_RW = 21;

    public const THEMATIC_MAPS_RIGHTS_R = 22;
    public const THEMATIC_MAPS_RIGHTS_RW = 23;

    public const COLLECTIONS_RIGHTS = 24;
    public const COLLECTIONS_RIGHTS_RW = 25;

    public const DASHBOARD_RIGHTS = 26;

    public const WAREHOUSE_USER_RIGHTS = 27;

    public const KVS_CUTTING_RIGHTS = 28;

    public const EXPORT_MASS_PAYMENT_RIGHTS = 29;

    public const CADASTRE_RIGHTS = 30;

    public const SLOPE_RIGHTS = 31;

    public const WAREHOUSE_ADMIN_RIGHTS = 32;

    public const WAREHOUSE_EDITOR_RIGHTS = 33;

    public const NO_RIGHTS_MESSAGE = 'no_module_rights';

    public const USER_RIGHTS_DELIMITER = ', ';

    // ContractTypes
    public const CONTRACT_TYPE_OWN = 1;
    public const CONTRACT_TYPE_LEASE = 2;
    public const CONTRACT_TYPE_RENT = 3;
    public const CONTRACT_TYPE_AGREEMENT = 4;
    public const CONTRACT_TYPE_JOINT_PROCESSING = 5;
    public const CONTRACT_TYPE_SALES = 7;

    public const ICON_CONTRACT_TYPE_OWN = self::CONTRACT_TYPE_OWN;
    public const ICON_CONTRACT_TYPE_LEASE = self::CONTRACT_TYPE_LEASE;
    public const ICON_CONTRACT_TYPE_RENT = self::CONTRACT_TYPE_RENT;
    public const ICON_CONTRACT_TYPE_SUBLEASE = 4;
    public const ICON_CONTRACT_TYPE_FROM_SUBLEASE = 5;
    public const ICON_CONTRACT_DEFAULT = 6;

    // Sublease contract types
    public const CONTRACT_SUBLEASE_TYPE_SUBLET = 1; // preotdavane
    public const CONTRACT_SUBLEASE_TYPE_SUBLEASE = 2; // prenaemane

    // Validation error codes
    public const STATUS_CODE_OK = 200;

    // Generic fields validation
    public const VALIDATION_VALID_FIELD = 200;
    public const VALIDATION_INVALID_KAD_IDENT = -33000;
    public const VALIDATION_INVALID_DIGITS_ONLY = -33001;
    public const VALIDATION_INVALID_NUMBER = -33002;
    public const VALIDATION_INVALID_TEXT = -33003;
    public const VALIDATION_INVALID_REQUIRED = -33004;
    public const VALIDATION_INVALID_NOT_NULL = -33005;
    public const VALIDATION_INVALID_INTEGER = -33006;
    public const VALIDATION_INVALID_DATE = -33007;
    public const VALIDATION_INVALID_TIMESTAMP = -33008;
    public const VALIDATION_INVALID_COLOR = -33009;
    public const VALIDATION_INVALID_FARMING_YEAR = -33010;
    public const VALIDATION_INVALID_CONTRACT_TYPE = -33011;
    public const VALIDATION_INVALID_ARRAY_TYPE = -33012;
    public const RENTA_TYPE_ALREADY_EXISTS = -33042;
    public const CONTRACT_GROUP_ALREADY_EXISTS = -33049;
    public const CONTRACT_GROUP_HAS_CONTRACTS = -33050;

    // Database error codes
    public const DATABASE_CONNECTION_ERROR = -33101;

    // Plots related error codes
    public const CONTRACT_AREA_EXCEEDS_PLOT_AREA = -33151;

    public const CONTRACT_RENT_AREA_EXCEEDS_PLOT_AREA = -33152;

    // Owner related error codes
    public const INVALID_OWNER_ID = -33301;

    // Can be used for checking if given number should be formated for KVS or KK.
    public const KVS_NUMBER_FORMAT = 6;
    public const KK_NUMBER_FORMAT = 8;

    public const PAID_SUPPORT_DATE_LAST = '2018-01-01';

    public const INFINITY_DATE = '9999-12-31 23:59:59';

    // Mass Export supported types
    public const MASS_PAYMENT_DSKXML = 'dskxml';
    public const MASS_PAYMENT_BULBANKTXT = 'bulbanktxt';
    public const MASS_PAYMENT_UBBXML = 'ubbxml';

    public const ANNEX_ACTION_REMOVED = 'removed';
    public const ANNEX_ACTION_ADDED = 'added';

    public const DECLARED_AREA_THRESHOLD = 0.01;

    public const DECLARATION_TYPE_69 = 'чл.69';
    public const DECLARATION_TYPE_70 = 'чл.70';

    public static $USERS_FLAGS = [
        1 => Config::USERS_SUPER_ADMIN_FLAG,
        2 => Config::USERS_ADMIN_FLAG,
        3 => Config::USERS_NORMAL,
        4 => Config::USERS_OPERATOR_FLAG,
        5 => Config::USERS_SUPPORT_FLAG,
        6 => Config::USERS_SALES_FLAG,
    ];

    public static $MODIFY_PERMISSIONS = [
        Config::USERS_SUPER_ADMIN_FLAG => [
            Config::USERS_SUPER_ADMIN_FLAG,
            Config::USERS_ADMIN_FLAG,
            Config::USERS_NORMAL,
            Config::USERS_OPERATOR_FLAG,
            Config::USERS_SUPPORT_FLAG,
            Config::USERS_SALES_FLAG,
        ],
        Config::USERS_ADMIN_FLAG => [Config::USERS_NORMAL],
        Config::USERS_NORMAL => [],
        Config::USERS_OPERATOR_FLAG => [],
        Config::USERS_SALES_FLAG => [Config::USERS_ADMIN_FLAG],
        Config::USERS_SUPPORT_FLAG => [Config::USERS_ADMIN_FLAG],
    ];

    public static $USERS_WITH_SUPPORT_PERMISSIONS = [
        Config::USERS_SUPER_ADMIN_FLAG,
        Config::USERS_SUPPORT_FLAG,
    ];

    public static $SYSTEM_MANAGEMENT_USER_LEVELS = [
        self::USERS_SUPER_ADMIN_FLAG,
        self::USERS_OPERATOR_FLAG,
        self::USERS_SUPPORT_FLAG,
        self::USERS_SALES_FLAG,
    ];

    /**
     * Defines rules which need to be fulfilled before allowing 'loginAs' operation.
     *
     * Syntax:
     *
     * 'role'
     * [] => [
     *  'restriction 1'
     *  [
     *      ['reverse'] => if set to 'true' - reverse operands (logged in becomes requested and vice versa)
     *      'self' => property of the logged in user
     *      'compare' => type of comparison
     *      'other' => property of the requested user (if no 'otherValue' is specified compare with this)
     *      'otherValue' => exact value to compare against (if omitted 'other' must be provided)
     *  ],
     *  ...
     * ]
     */
    public static $LOGIN_AS_POLICY = [
        Config::USERS_SUPER_ADMIN_FLAG => [],
        Config::USERS_ADMIN_FLAG => [
            [
                'self' => 'group_id',
                'compare' => '=',
                'other' => 'parent_id',
            ],
            [
                'reverse' => true,
                'self' => 'level',
                'compare' => '=',
                'otherValue' => Config::USERS_NORMAL,
            ],
        ],
        Config::USERS_SUPPORT_FLAG => [
            [
                'self' => 'level',
                'compare' => 'nin',
                'otherValue' => [
                    Config::USERS_SUPER_ADMIN_FLAG,
                    Config::USERS_SUPPORT_FLAG,
                ],
            ],
        ],
        Config::USERS_SALES_FLAG => [
            [
                'self' => 'level',
                'compare' => 'nin',
                'otherValue' => [
                    Config::USERS_SUPER_ADMIN_FLAG,
                    Config::USERS_SUPPORT_FLAG,
                    Config::USERS_SALES_FLAG,
                ],
            ],
        ],
    ];

    public static $SUBSCRIPTIONS = [
        Config::SUBSCRIPTION_MAP => 'Карта',
        Config::SUBSCRIPTION_PLOTS => 'Имоти',
        Config::SUBSCRIPTION_AGRO => 'Агротехника',
    ];

    public static $USER_RIGHTS_MAP = [
        Config::MAP_RIGHTS_R => ['id' => Config::MAP_RIGHTS_R, 'name' => 'Карта четене', 'visible' => false],
        Config::MAP_RIGHTS_RW => ['id' => Config::MAP_RIGHTS_RW, 'name' => 'Карта', 'visible' => true],

        Config::PLOT_RIGHTS_R => ['id' => Config::PLOT_RIGHTS_R, 'name' => 'Имоти четене', 'visible' => false],
        Config::PLOT_RIGHTS_RW => ['id' => Config::PLOT_RIGHTS_RW, 'name' => 'Имоти', 'visible' => true],

        Config::SUBSIDY_RIGHTS => ['id' => Config::SUBSIDY_RIGHTS, 'name' => 'Субсидии', 'visible' => false],
        Config::SUBSIDY_RIGHTS_RW => ['id' => Config::SUBSIDY_RIGHTS_RW, 'name' => 'Субсидии', 'visible' => true],

        Config::AGRO_RIGHTS => ['id' => Config::AGRO_RIGHTS, 'name' => 'Агротехника', 'visible' => false],
        Config::AGRO_RIGHTS_RW => ['id' => Config::AGRO_RIGHTS_RW, 'name' => 'Агротехника', 'visible' => true],

        Config::CONTRACTS_READ_RIGHTS => ['id' => Config::CONTRACTS_READ_RIGHTS, 'name' => 'Договори четене', 'visible' => false],
        Config::CONTRACTS_WRITE_RIGHTS => ['id' => Config::CONTRACTS_WRITE_RIGHTS, 'name' => 'Договори', 'visible' => true],

        Config::SALES_CONTRACTS_RIGHTS_R => ['id' => Config::SALES_CONTRACTS_RIGHTS_R, 'name' => 'Договори за продажба четене', 'visible' => false],
        Config::SALES_CONTRACTS_RIGHTS_RW => ['id' => Config::SALES_CONTRACTS_RIGHTS_RW, 'name' => 'Договори за продажба', 'visible' => true],

        Config::HYPOTHECS_RIGHTS_R => ['id' => Config::HYPOTHECS_RIGHTS_R, 'name' => 'Ипотеки четене', 'visible' => false],
        Config::HYPOTHECS_RIGHTS_RW => ['id' => Config::HYPOTHECS_RIGHTS_RW, 'name' => 'Ипотеки', 'visible' => true],

        Config::THEMATIC_MAPS_RIGHTS_R => ['id' => Config::THEMATIC_MAPS_RIGHTS_R, 'name' => 'Тематични карти четене', 'visible' => false],
        Config::THEMATIC_MAPS_RIGHTS_RW => ['id' => Config::THEMATIC_MAPS_RIGHTS_RW, 'name' => 'Тематични карти', 'visible' => true],

        Config::COLLECTIONS_RIGHTS => ['id' => Config::COLLECTIONS_RIGHTS, 'name' => 'Вземания четене', 'visible' => false],
        Config::COLLECTIONS_RIGHTS_RW => ['id' => Config::COLLECTIONS_RIGHTS_RW, 'name' => 'Вземания', 'visible' => true],

        Config::CONTRACTS_OWN_WRITE_RIGHTS => ['id' => Config::CONTRACTS_OWN_WRITE_RIGHTS, 'name' => 'Договори за собственост', 'visible' => true],

        Config::EQUITY_RIGHTS => ['id' => Config::EQUITY_RIGHTS, 'name' => 'Дялов капитал', 'visible' => true],

        Config::DASHBOARD_RIGHTS => ['id' => Config::DASHBOARD_RIGHTS, 'name' => 'Дашборд', 'visible' => true],
        Config::WAREHOUSE_USER_RIGHTS => ['id' => Config::WAREHOUSE_USER_RIGHTS, 'name' => 'Склад', 'visible' => true],
        Config::WAREHOUSE_ADMIN_RIGHTS => ['id' => Config::WAREHOUSE_ADMIN_RIGHTS, 'name' => 'Склад - админ', 'visible' => true],
        Config::WAREHOUSE_EDITOR_RIGHTS => ['id' => Config::WAREHOUSE_EDITOR_RIGHTS, 'name' => 'Склад - едитор', 'visible' => true],

        Config::KVS_CUTTING_RIGHTS => ['id' => Config::KVS_CUTTING_RIGHTS, 'name' => 'Пресичане на КВС', 'visible' => true],
        Config::EXPORT_MASS_PAYMENT_RIGHTS => ['id' => Config::EXPORT_MASS_PAYMENT_RIGHTS, 'name' => 'Експорт масови плащания', 'visible' => true],
        Config::CADASTRE_RIGHTS => ['id' => Config::CADASTRE_RIGHTS, 'name' => 'Кдастър', 'visible' => true],
        Config::SLOPE_RIGHTS => ['id' => Config::SLOPE_RIGHTS, 'name' => 'Наклон на парцел', 'visible' => true],
    ];

    public static $contractTypeIconsMap = [
        self::ICON_CONTRACT_TYPE_OWN => 'icon-contract-type-own',
        self::ICON_CONTRACT_TYPE_LEASE => 'icon-contract-type-lease',
        self::ICON_CONTRACT_TYPE_RENT => 'icon-contract-type-rent',
        self::ICON_CONTRACT_TYPE_SUBLEASE => 'icon-contract-type-sublease',
        self::ICON_CONTRACT_TYPE_FROM_SUBLEASE => 'icon-contract-type-sublease',
        self::ICON_CONTRACT_DEFAULT => 'icon-tree-document',
    ];

    public static $NTP = [
        [
            'value' => 'ГЛП',
            'text' => 'Горски ливади и пасища',
        ],
        [
            'value' => 'ДМ',
            'text' => 'Дворни места',
        ],
        [
            'value' => 'ДТН',
            'text' => 'Други трайни насаждения',
        ],
        [
            'value' => 'ЕПЛ',
            'text' => 'Естествени пасища и ливади',
        ],
        [
            'value' => 'КСТ',
            'text' => 'Крайселищни територии',
        ],
        [
            'value' => 'ЛН',
            'text' => 'Лозови насаждения',
        ],
        [
            'value' => 'НМ',
            'text' => 'Населени места',
        ],
        [
            'value' => 'ОЗ',
            'text' => 'Обработваеми земи',
        ],
        [
            'value' => 'ОН',
            'text' => 'Овощни насаждения',
        ],
        [
            'value' => 'ПМЛ',
            'text' => 'Пасища, мери и ливади',
        ],
        [
            'value' => 'СЗП',
            'text' => 'Смесено земеползване',
        ],
        [
            'value' => 'ТН',
            'text' => 'Трайни насъждения',
        ],
    ];

    public static $NAMESPACES = [
        'UserDbController' => 'TF\Engine\Plugins\Core\UserDb\UserDbController',
        'UserDbPlotsController' => 'TF\Engine\Plugins\Core\UserDbPlots\UserDbPlotsController',
        'UserDbForIsakController' => 'TF\Engine\Plugins\Core\UserDbForIsak\UserDbForIsakController',
    ];

    public static $ANNEX_ACTIONS_MAP = [
        self::ANNEX_ACTION_REMOVED,
        self::ANNEX_ACTION_ADDED,
    ];

    public static $FILE_STATUSES_MAP = [
        LOADING_FILE => 'Processing',
        SUCCESSFULLY_TREATED => 'Successfully processed',
        ERROR_INVALID_SHAPE => 'Invalid Shape file',
        ERROR_INVALID_DBF => 'Invalid DBF file', // 3
        ERROR_INVALID_ARCHIVE => 'Invalid archive', // 4
        ERROR_INVALID_GEOMETRY => 'Invalid polygon geometry', // 5
        ERROR_INVALID_ISAK_FILE => 'Invalid ISAK file', // 6
        ERROR_RUNTIME => 'System error during processing', // 7
        ERROR_INVALID_TABLE_STRUCTURE => 'Invalid attribute information', // 8
        ERROR_INVALID_FILE_DATA => 'Intersection with existing objects', // 9
        ERROR_WAITING_DEFINITION => 'Waiting for definition', // 10
        ERROR_WAITING_COPYING => 'Copying data', // 11
        ERROR_INVALID_CRS => 'Invalid projection', // 12
        ERROR_NOT_ALLOWED_ADDING => 'Not allowed to add new data', // 13
        ERROR_INCORRECT_ENCODING => 'Encoding problem', // 14
        ERROR_INCORRECT_ENCODING_FIELD => 'Encoding problem in the column name of the table', // 16
        ERROR_MISSING_COLUMN => 'Missing column', // 15
        PARTIALLY_PROCESSED => 'Partially processed', // 17
        INCONSISTENT_FILE_TYPE => 'Inconsistent file type', //  18
        LOADING_FILE_NOW => 'Processing started...', //   19
        ERROR_READING_SHAPE_OBJECT => 'Error reading shape object', //  20
        ERROR_GEOMETRY_COLLECTION => 'Error: Geometry Collection present!', //  21
        NOT_UPDATED_CONTRACTS => 'Contracts not updated', //  22
        ERROR_MISSING_KVS_EKATTE => 'Organization does not own ekatte', //  25
    ];
}
