<div>
    <div style="float: left; margin: 0px 10px 10px 10px">
        <fieldset style="border: 1px solid #000;" >
            <legend style="margin-left: 10px; font-style: italic; font-weight: bold;">Информация</legend>
            <p style="padding: 5px; font-style: italic; font-weight: bold;">
				С този инструмент може да промените стойностите за рента за всички филтрирани в момента договори, без тези, които са автоматично създадени от подмодул "Преотдадени". Ако оставите празно поле, ще се запази първоначалната стойност от въведения договор. Ако напишете 0 (нула) в полето за рента в пари или в полетата за количество рента в натура ще занулите стойностите на съответния тип рента във филтрираните договори. Ако напишете „-„ в полето за количество рента в натура ще премахнете съответния тип натура във филтрираните договори.
			</p>
        </fieldset>

        <fieldset style="border: 1px solid #000; height: 80px; margin-top: 5px; padding-bottom: 5px;" id="box-area-for-renta">
            <legend style="margin-left: 10px; font-style: italic; font-weight: bold;">Площ за рента</legend>
            <p style="padding: 5px; padding-bottom: 0px;">
                <input type="checkbox" id="multiedit-area-for-rent-checkbox" />
                <label for="multiedit-area-for-rent-checkbox">Приравни площта за рента към площта по сечение</label>
            </p>
            <p style="padding: 5px; font-style: italic;">
                * Ако площта по сечение е по-голяма от площта по договор, площта за рента ще бъде приравнена към площта по договор
            </p>
        </fieldset>

		<fieldset style="border: 1px solid #000; height: 80px; margin-top: 5px; padding-bottom: 5px;" id="box-area-for-renta-to-contract">
			<legend style="margin-left: 10px; font-style: italic; font-weight: bold;">Площ за договор</legend>
			<p style="padding: 5px; padding-bottom: 0px;">
				<input type="checkbox" id="multiedit-area-for-rent-to-contract-checkbox" />
				<label for="multiedit-area-for-rent-to-contract-checkbox">Приравни площта за рента към площта по договор</label>
			</p>
			<p style="padding: 5px; font-style: italic;">
				* Ако площта по рента е по-голяма от площта по договор, площта за рента ще бъде приравнена към площта по договор
			</p>
		</fieldset>

		<fieldset style="border: 1px solid #000; height: 80px; margin-top: 5px; padding-bottom: 5px;" id="box-area-for-renta-to-contract">
			<legend style="margin-left: 10px; font-style: italic; font-weight: bold;">Обработваема площ</legend>
			<p style="padding: 5px; padding-bottom: 0px;">
				<input type="checkbox" id="multiedit-area-for-rent-to-arable-checkbox" />
				<label for="multiedit-area-for-rent-to-arable-checkbox">Приравни площа за рента към колона Обработваема площ</label>
			</p>
			<p style="padding: 5px; font-style: italic;">
				* Ако обработваемата площ е по-голяма от площта по договор, площта за рента ще бъде приравнена към площта по договор
			</p>
		</fieldset>

        <fieldset style="border: 1px solid #000; height: 200px; margin-top: 5px; padding-bottom: 10px;" id="box-renta-fields">
            <legend style="margin-left: 10px; font-style: italic; font-weight: bold;">Рента</legend>
            <div style="overflow:auto;height: 153px;"> 
	            <table id="multi-renta-table" width="280px" class="filters" cellspacing="0" cellpadding="0" >
		            <tr><td colspan="3">
		            <table>
		                <tr>
		                    <td style="text-align: left; width: 43px; padding-left: 10px;">В лева</td>
		                    <td id="multi-renta" style="width: 120px;">
								<input type="text" style="width: 110px;" />
							</td>
						</tr>
					</table>
					</td>
					</tr>
					<tr>
						<td style="text-align: left;  padding-left: 10px;">Тип натура</td>
						<td style="text-align: center; width: 73px;">В натура-кол.</td>
					</tr>
					<tr>
	                    <td style="width: 215px;  padding-left: 10px;">
							<input id="multi-renta-type-cb-0" type="text" style="width: 205px;" />
						</td>
						<td style="width: 95px;">
							<input id="multi-renta-natura-0" type="text" style="width: 95px;" />
						</td>
						<td>
							<a id="multi-remove-renta-btn" href="javaScript:void(0)" class="easyui-linkbutton" onClick="removeMultiRentaField();" data-options="iconCls:'icon-cancel'"></a>
						</td>
						<td>
							<a id="multi-add-new-renta-btn" href="javaScript:void(0)" class="easyui-linkbutton" onClick="addRentaFieldMulti();" data-options="iconCls:'icon-add'"></a>
						</td>
	                </tr>
	            </table>
            </div> 
        </fieldset>

		<p style="padding: 5px; padding-bottom: 0px;">
			<input type="checkbox" id="multiedit-include-annex" />
			<label for=""multiedit-include-annex">Приложи и в анекси</label>
		</p>
    </div>
    <div style="clear: both"></div>
	<table width="100%" style="margin-bottom: 15px; margin-top: 5px;" cellspacing="0" cellpadding="0">
	    <tr>       
	        <td colspan="2" style="text-align:center;">
				<a id="multibtnsave" href="javaScript:void(0)" class="easyui-linkbutton" onClick="executeMultiEditContracts();" data-options="iconCls:'icon-save'">Запази</a>
	            <a id="multibtnclose" href="javaScript:void(0)" class="easyui-linkbutton" onClick="jQuery('#win-multiedit-contracts').window('close');" data-options="iconCls:'icon-cancel'">Откажи</a>
	        </td>
	    </tr>
	</table>
</div>